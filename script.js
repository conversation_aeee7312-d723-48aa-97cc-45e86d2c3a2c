// بيانات الرواتب من الجدول الأصلي (بالآلاف)
const salariesData = {
    "أ": {
        "استيب 1": 2413000,
        "استيب 2": 2496000,
        "استيب 3": 2579000,
        "استيب 4": 2662000,
        "استيب 5": 2745000,
        "استيب 6": 2828000,
        "استيب 7": 2911000,
        "استيب 8": 2994000,
        "استيب 9": 3077000,
        "استيب 10": 3160000,
        "استيب 11": 3243000
    },
    "ب": {
        "استيب 1": 2000000,
        "استيب 2": 2083000,
        "استيب 3": 2166000,
        "استيب 4": 2249000,
        "استيب 5": 2332000,
        "استيب 6": 2415000,
        "استيب 7": 2498000,
        "استيب 8": 2581000,
        "استيب 9": 2664000,
        "استيب 10": 2747000,
        "استيب 11": 2830000
    },
    "ب1": {
        "استيب 1": 1500000,
        "استيب 2": 1583000,
        "استيب 3": 1666000,
        "استيب 4": 1749000,
        "استيب 5": 1832000,
        "استيب 6": 1915000,
        "استيب 7": 1998000,
        "استيب 8": 2081000,
        "استيب 9": 2164000,
        "استيب 10": 2247000,
        "استيب 11": 2330000
    },
    "الدرجة 1": {
        "استيب 1": 910000,
        "استيب 2": 930000,
        "استيب 3": 950000,
        "استيب 4": 970000,
        "استيب 5": 990000,
        "استيب 6": 1010000,
        "استيب 7": 1030000,
        "استيب 8": 1050000,
        "استيب 9": 1070000,
        "استيب 10": 1090000,
        "استيب 11": 1110000
    },
    "الدرجة 2": {
        "استيب 1": 723000,
        "استيب 2": 740000,
        "استيب 3": 757000,
        "استيب 4": 774000,
        "استيب 5": 791000,
        "استيب 6": 808000,
        "استيب 7": 825000,
        "استيب 8": 842000,
        "استيب 9": 859000,
        "استيب 10": 876000,
        "استيب 11": 893000
    },
    "الدرجة 3": {
        "استيب 1": 600000,
        "استيب 2": 610000,
        "استيب 3": 620000,
        "استيب 4": 630000,
        "استيب 5": 640000,
        "استيب 6": 650000,
        "استيب 7": 660000,
        "استيب 8": 670000,
        "استيب 9": 680000,
        "استيب 10": 690000,
        "استيب 11": 700000
    },
    "الدرجة 4": {
        "استيب 1": 509000,
        "استيب 2": 517000,
        "استيب 3": 525000,
        "استيب 4": 533000,
        "استيب 5": 541000,
        "استيب 6": 549000,
        "استيب 7": 557000,
        "استيب 8": 565000,
        "استيب 9": 573000,
        "استيب 10": 581000,
        "استيب 11": 589000
    },
    "الدرجة 5": {
        "استيب 1": 429000,
        "استيب 2": 435000,
        "استيب 3": 441000,
        "استيب 4": 447000,
        "استيب 5": 453000,
        "استيب 6": 459000,
        "استيب 7": 465000,
        "استيب 8": 471000,
        "استيب 9": 477000,
        "استيب 10": 483000,
        "استيب 11": 489000
    },
    "الدرجة 6": {
        "استيب 1": 362000,
        "استيب 2": 368000,
        "استيب 3": 374000,
        "استيب 4": 380000,
        "استيب 5": 386000,
        "استيب 6": 392000,
        "استيب 7": 398000,
        "استيب 8": 404000,
        "استيب 9": 410000,
        "استيب 10": 416000,
        "استيب 11": 422000
    },
    "الدرجة 7": {
        "استيب 1": 296000,
        "استيب 2": 302000,
        "استيب 3": 308000,
        "استيب 4": 314000,
        "استيب 5": 320000,
        "استيب 6": 326000,
        "استيب 7": 332000,
        "استيب 8": 338000,
        "استيب 9": 344000,
        "استيب 10": 350000,
        "استيب 11": 356000
    },
    "الدرجة 8": {
        "استيب 1": 260000,
        "استيب 2": 263000,
        "استيب 3": 266000,
        "استيب 4": 269000,
        "استيب 5": 272000,
        "استيب 6": 275000,
        "استيب 7": 278000,
        "استيب 8": 281000,
        "استيب 9": 284000,
        "استيب 10": 287000,
        "استيب 11": 290000
    },
    "الدرجة 9": {
        "استيب 1": 210000,
        "استيب 2": 213000,
        "استيب 3": 216000,
        "استيب 4": 219000,
        "استيب 5": 222000,
        "استيب 6": 225000,
        "استيب 7": 228000,
        "استيب 8": 231000,
        "استيب 9": 234000,
        "استيب 10": 237000,
        "استيب 11": 240000
    },
    "الدرجة 10": {
        "استيب 1": 170000,
        "استيب 2": 173000,
        "استيب 3": 176000,
        "استيب 4": 179000,
        "استيب 5": 182000,
        "استيب 6": 185000,
        "استيب 7": 188000,
        "استيب 8": 191000,
        "استيب 9": 194000,
        "استيب 10": 197000,
        "استيب 11": 200000
    }
};

// بيانات الضرائب من الجدول الأصلي - مبالغ ثابتة وليس نسب
const taxesData = {
    "a": [
        { min: 242500, max: 247499, tax: 86 },
        { min: 247500, max: 252499, tax: 215 },
        { min: 252500, max: 257499, tax: 344 },
        { min: 257500, max: 262499, tax: 474 },
        { min: 262500, max: 267499, tax: 603 },
        { min: 267500, max: 272499, tax: 804 },
        { min: 272500, max: 277499, tax: 1019 },
        { min: 277500, max: 282499, tax: 1235 },
        { min: 282500, max: 287499, tax: 1450 },
        { min: 287500, max: 292499, tax: 1666 },
        { min: 292500, max: 297499, tax: 2096 },
        { min: 297500, max: 302499, tax: 2527 },
        { min: 302500, max: 307499, tax: 2958 },
        { min: 307500, max: 312499, tax: 3389 },
        { min: 312500, max: 317499, tax: 3820 },
        { min: 317500, max: 322499, tax: 4251 },
        { min: 322500, max: 327499, tax: 4682 },
        { min: 327500, max: 332499, tax: 5113 },
        { min: 332500, max: 337499, tax: 5544 },
        { min: 337500, max: 342499, tax: 6045 },
        { min: 342500, max: 347499, tax: 6692 },
        { min: 347500, max: 352499, tax: 7338 },
        { min: 352500, max: 357499, tax: 7985 },
        { min: 357500, max: 362499, tax: 8631 },
        { min: 362500, max: 367499, tax: 9278 },
        { min: 367500, max: 372499, tax: 9924 },
        { min: 372500, max: 377499, tax: 10571 },
        { min: 377500, max: 382499, tax: 11217 },
        { min: 382500, max: 387499, tax: 11864 },
        { min: 387500, max: 392499, tax: 12510 },
        { min: 392500, max: 397499, tax: 13157 },
        { min: 397500, max: 402499, tax: 13803 },
        { min: 402500, max: 407499, tax: 14450 },
        { min: 407500, max: 412499, tax: 15096 },
        { min: 412500, max: 417499, tax: 15743 },
        { min: 417500, max: 422499, tax: 16389 },
        { min: 422500, max: 427499, tax: 17036 },
        { min: 427500, max: 432499, tax: 17682 },
        { min: 432500, max: 437499, tax: 18329 },
        { min: 437500, max: 442499, tax: 18975 },
        { min: 442500, max: 447499, tax: 19622 },
        { min: 447500, max: 452499, tax: 20268 },
        { min: 452500, max: 457499, tax: 20915 },
        { min: 457500, max: 462499, tax: 21561 },
        { min: 462500, max: 467499, tax: 22208 },
        { min: 467500, max: 472499, tax: 22854 },
        { min: 472500, max: 477499, tax: 23501 },
        { min: 477500, max: 482499, tax: 24147 },
        { min: 482500, max: 487499, tax: 24794 },
        { min: 487500, max: 492499, tax: 25440 },
        { min: 492500, max: 497499, tax: 26087 },
        { min: 497500, max: 502499, tax: 26733 },
        { min: 502500, max: 507499, tax: 27380 },
        { min: 507500, max: 512499, tax: 28026 },
        { min: 512500, max: 517499, tax: 28673 },
        { min: 517500, max: 522499, tax: 29319 },
        { min: 522500, max: 527499, tax: 29966 },
        { min: 527500, max: 532499, tax: 30612 },
        { min: 532500, max: 537499, tax: 31259 },
        { min: 537500, max: 542499, tax: 31905 },
        { min: 542500, max: 547499, tax: 32552 },
        { min: 547500, max: 552499, tax: 33198 },
        { min: 552500, max: 557499, tax: 33845 },
        { min: 557500, max: 562499, tax: 34491 },
        { min: 562500, max: 567499, tax: 35138 },
        { min: 567500, max: 572499, tax: 35784 },
        { min: 572500, max: 577499, tax: 36431 },
        { min: 577500, max: 582499, tax: 37077 },
        { min: 582500, max: 587499, tax: 37724 },
        { min: 587500, max: 592499, tax: 38370 },
        { min: 592500, max: 597499, tax: 39017 },
        { min: 597500, max: 602499, tax: 39663 },
        { min: 602500, max: 607499, tax: 40310 },
        { min: 607500, max: 612499, tax: 40956 },
        { min: 612500, max: 617499, tax: 41603 },
        { min: 617500, max: 622499, tax: 42249 },
        { min: 622500, max: 627499, tax: 42896 },
        { min: 627500, max: 632499, tax: 43542 },
        { min: 632500, max: 637499, tax: 44189 },
        { min: 637500, max: 642499, tax: 44835 },
        { min: 642500, max: 647499, tax: 45482 },
        { min: 647500, max: 652499, tax: 46128 },
        { min: 652500, max: 657499, tax: 46775 },
        { min: 657500, max: 662499, tax: 47421 },
        { min: 662500, max: 667499, tax: 48068 },
        { min: 667500, max: 672499, tax: 48714 },
        { min: 672500, max: 677499, tax: 49361 },
        { min: 677500, max: 682499, tax: 50007 },
        { min: 682500, max: 687499, tax: 50654 },
        { min: 687500, max: 692499, tax: 51300 },
        { min: 692500, max: 697499, tax: 51947 },
        { min: 697500, max: 702499, tax: 52593 },
        { min: 702500, max: 707499, tax: 53240 },
        { min: 707500, max: 712499, tax: 53886 },
        { min: 712500, max: 717499, tax: 54533 },
        { min: 717500, max: 722499, tax: 55179 },
        { min: 722500, max: 727499, tax: 55826 },
        { min: 727500, max: 732499, tax: 56472 },
        { min: 732500, max: 737499, tax: 57118 },
        { min: 737500, max: 742499, tax: 57765 },
        { min: 742500, max: 747499, tax: 58411 },
        { min: 747500, max: 752499, tax: 59058 },
        { min: 752500, max: 757499, tax: 59704 },
        { min: 757500, max: 762499, tax: 60351 },
        { min: 762500, max: 767499, tax: 60997 },
        { min: 767500, max: 772499, tax: 61644 },
        { min: 772500, max: 777499, tax: 62290 },
        { min: 777500, max: 782499, tax: 62937 },
        { min: 782500, max: 787499, tax: 63583 },
        { min: 787500, max: 792499, tax: 64230 },
        { min: 792500, max: 797499, tax: 64876 },
        { min: 797500, max: 802499, tax: 65523 },
        { min: 802500, max: 807499, tax: 66169 },
        { min: 807500, max: 812499, tax: 66816 },
        { min: 812500, max: 817499, tax: 67462 },
        { min: 817500, max: 822499, tax: 68109 },
        { min: 822500, max: 827499, tax: 68755 },
        { min: 827500, max: 832499, tax: 69402 },
        { min: 832500, max: 837499, tax: 70048 },
        { min: 837500, max: 842499, tax: 70695 },
        { min: 842500, max: 847499, tax: 71341 },
        { min: 847500, max: 852499, tax: 71988 },
        { min: 852500, max: 857499, tax: 72634 },
        { min: 857500, max: 862499, tax: 73281 },
        { min: 862500, max: 867499, tax: 73927 },
        { min: 867500, max: 872499, tax: 74574 },
        { min: 872500, max: 877499, tax: 75220 },
        { min: 877500, max: 882499, tax: 75867 },
        { min: 882500, max: 887499, tax: 76513 },
        { min: 887500, max: 892499, tax: 77160 },
        { min: 892500, max: 897499, tax: 77806 },
        { min: 897500, max: 902499, tax: 78453 },
        { min: 902500, max: 907499, tax: 79099 },
        { min: 907500, max: 912499, tax: 79746 },
        { min: 912500, max: 917499, tax: 80392 },
        { min: 917500, max: 922499, tax: 81039 },
        { min: 922500, max: 927499, tax: 81685 },
        { min: 927500, max: 932499, tax: 82332 },
        { min: 932500, max: 937499, tax: 82978 },
        { min: 937500, max: 942499, tax: 83625 },
        { min: 942500, max: 947499, tax: 84271 },
        { min: 947500, max: 952499, tax: 84918 },
        { min: 952500, max: 957499, tax: 85564 },
        { min: 957500, max: 962499, tax: 86211 },
        { min: 962500, max: 967499, tax: 86857 },
        { min: 967500, max: 972499, tax: 87504 },
        { min: 972500, max: 977499, tax: 88150 },
        { min: 977500, max: 982499, tax: 88797 },
        { min: 982500, max: 987499, tax: 89443 },
        { min: 987500, max: 992499, tax: 90090 },
        { min: 992500, max: 997499, tax: 90736 },
        { min: 997500, max: 1002499, tax: 91383 },
        { min: 1002500, max: 1007499, tax: 92029 },
        { min: 1007500, max: 1012499, tax: 92676 },
        { min: 1012500, max: 1017499, tax: 93322 },
        { min: 1017500, max: 1022499, tax: 93969 },
        { min: 1022500, max: 1027499, tax: 94615 },
        { min: 1027500, max: 1032499, tax: 95262 },
        { min: 1032500, max: 1037499, tax: 95908 },
        { min: 1037500, max: 1042499, tax: 96555 },
        { min: 1042500, max: 1047499, tax: 97201 },
        { min: 1047500, max: 1052499, tax: 97848 },
        { min: 1052500, max: 1057499, tax: 98494 },
        { min: 1057500, max: 1062499, tax: 99141 },
        { min: 1062500, max: 1067499, tax: 99787 },
        { min: 1067500, max: 1072499, tax: 100434 },
        { min: 1072500, max: 1077499, tax: 101080 },
        { min: 1077500, max: 1082499, tax: 101727 },
        { min: 1082500, max: 1087499, tax: 102373 },
        { min: 1087500, max: 1092499, tax: 103020 },
        { min: 1092500, max: 1097499, tax: 103666 },
        { min: 1097500, max: 1102499, tax: 104313 },
        { min: 1102500, max: 1107499, tax: 104959 },
        { min: 1107500, max: 1112499, tax: 105606 },
        { min: 1112500, max: 1117499, tax: 106252 },
        { min: 1117500, max: 1122499, tax: 106899 },
        { min: 1122500, max: 1127499, tax: 107545 },
        { min: 1127500, max: 1132499, tax: 108192 },
        { min: 1132500, max: 1137499, tax: 108838 },
        { min: 1137500, max: 1142499, tax: 109485 },
        { min: 1142500, max: 1147499, tax: 110131 },
        { min: 1147500, max: 1152499, tax: 110778 },
        { min: 1152500, max: 1157499, tax: 111424 },
        { min: 1157500, max: 1162499, tax: 112071 },
        { min: 1162500, max: 1167499, tax: 112717 },
        { min: 1167500, max: 1172499, tax: 113364 },
        { min: 1172500, max: 1177499, tax: 114010 },
        { min: 1177500, max: 1182499, tax: 114657 },
        { min: 1182500, max: 1187499, tax: 115303 },
        { min: 1187500, max: 1192499, tax: 115950 },
        { min: 1192500, max: 1197499, tax: 116596 },
        { min: 1197500, max: 1202499, tax: 117243 },
        { min: 1202500, max: 1207499, tax: 117889 },
        { min: 1207500, max: 1212499, tax: 118536 },
        { min: 1212500, max: 1217499, tax: 119182 },
        { min: 1217500, max: 1222499, tax: 119829 },
        { min: 1222500, max: 1227499, tax: 120475 },
        { min: 1227500, max: 1232499, tax: 121122 },
        { min: 1232500, max: 1237499, tax: 121768 },
        { min: 1237500, max: 1242499, tax: 122415 },
        { min: 1242500, max: 1247499, tax: 123061 },
        { min: 1247500, max: 1252499, tax: 123708 },
        { min: 1252500, max: 1257499, tax: 124354 },
        { min: 1257500, max: 1262499, tax: 125001 },
        { min: 1262500, max: 1267499, tax: 125647 },
        { min: 1267500, max: 1272499, tax: 126294 },
        { min: 1272500, max: 1277499, tax: 126940 },
        { min: 1277500, max: 1282499, tax: 127587 },
        { min: 1282500, max: 1287499, tax: 128233 },
        { min: 1287500, max: 1292499, tax: 128880 },
        { min: 1292500, max: 1297499, tax: 129526 },
        { min: 1297500, max: 1302499, tax: 130173 },
        { min: 1302500, max: 1307499, tax: 130819 },
        { min: 1307500, max: 1312499, tax: 131466 },
        { min: 1312500, max: 1317499, tax: 132112 },
        { min: 1317500, max: 1322499, tax: 132759 },
        { min: 1322500, max: 1327499, tax: 133405 },
        { min: 1327500, max: 1332499, tax: 134052 },
        { min: 1332500, max: 1337499, tax: 134698 },
        { min: 1337500, max: 1342499, tax: 135345 },
        { min: 1342500, max: 1347499, tax: 135991 },
        { min: 1347500, max: 1352499, tax: 136638 },
        { min: 1352500, max: 1357499, tax: 137284 },
        { min: 1357500, max: 1362499, tax: 137931 },
        { min: 1362500, max: 1367499, tax: 138577 },
        { min: 1367500, max: 1372499, tax: 139224 },
        { min: 1372500, max: 1377499, tax: 139870 },
        { min: 1377500, max: 1382499, tax: 140517 },
        { min: 1382500, max: 1387499, tax: 141163 },
        { min: 1387500, max: 1392499, tax: 141810 },
        { min: 1392500, max: 1397499, tax: 142456 },
        { min: 1397500, max: 1402499, tax: 143103 },
        { min: 1402500, max: 1407499, tax: 143749 },
        { min: 1407500, max: 1412499, tax: 144396 },
        { min: 1412500, max: 1417499, tax: 145042 },
        { min: 1417500, max: 1422499, tax: 145689 },
        { min: 1422500, max: 1427499, tax: 146335 },
        { min: 1427500, max: 1432499, tax: 146982 },
        { min: 1432500, max: 1437499, tax: 147628 },
        { min: 1437500, max: 1442499, tax: 148275 },
        { min: 1442500, max: 1447499, tax: 148921 },
        { min: 1447500, max: 1452499, tax: 149568 },
        { min: 1452500, max: 1457499, tax: 150214 },
        { min: 1457500, max: 1462499, tax: 150861 },
        { min: 1462500, max: 1467499, tax: 151507 },
        { min: 1467500, max: 1472499, tax: 152154 },
        { min: 1472500, max: 1477499, tax: 152800 },
        { min: 1477500, max: 1482499, tax: 153447 },
        { min: 1482500, max: 1487499, tax: 154093 },
        { min: 1487500, max: 1492499, tax: 154740 },
        { min: 1492500, max: 1497499, tax: 155386 },
        { min: 1497500, max: 1502499, tax: 156033 }
    ],
    "0": [ // متزوج بدون أطفال
        { min: 242500, max: 247499, tax: 0 },
        { min: 247500, max: 252499, tax: 0 },
        { min: 252500, max: 257499, tax: 0 },
        { min: 257500, max: 262499, tax: 0 },
        { min: 262500, max: 267499, tax: 0 },
        { min: 267500, max: 272499, tax: 0 },
        { min: 272500, max: 277499, tax: 0 },
        { min: 277500, max: 282499, tax: 0 },
        { min: 282500, max: 287499, tax: 0 },
        { min: 287500, max: 292499, tax: 0 },
        { min: 292500, max: 297499, tax: 0 },
        { min: 297500, max: 302499, tax: 0 },
        { min: 302500, max: 307499, tax: 0 },
        { min: 307500, max: 312499, tax: 0 },
        { min: 312500, max: 317499, tax: 0 },
        { min: 317500, max: 322499, tax: 0 },
        { min: 322500, max: 327499, tax: 0 },
        { min: 327500, max: 332499, tax: 0 },
        { min: 332500, max: 337499, tax: 0 },
        { min: 337500, max: 342499, tax: 0 },
        { min: 342500, max: 347499, tax: 0 },
        { min: 347500, max: 352499, tax: 0 },
        { min: 352500, max: 357499, tax: 0 },
        { min: 357500, max: 362499, tax: 0 },
        { min: 362500, max: 367499, tax: 0 },
        { min: 367500, max: 372499, tax: 0 },
        { min: 372500, max: 377499, tax: 0 },
        { min: 377500, max: 382499, tax: 0 },
        { min: 382500, max: 387499, tax: 0 },
        { min: 387500, max: 392499, tax: 0 },
        { min: 392500, max: 397499, tax: 0 },
        { min: 397500, max: 402499, tax: 0 },
        { min: 402500, max: 407499, tax: 0 },
        { min: 407500, max: 412499, tax: 0 },
        { min: 412500, max: 417499, tax: 0 },
        { min: 417500, max: 422499, tax: 0 },
        { min: 422500, max: 427499, tax: 0 },
        { min: 427500, max: 432499, tax: 0 },
        { min: 432500, max: 437499, tax: 0 },
        { min: 437500, max: 442499, tax: 128 },
        { min: 442500, max: 447499, tax: 258 },
        { min: 447500, max: 452499, tax: 387 },
        { min: 452500, max: 457499, tax: 516 },
        { min: 457500, max: 462499, tax: 659 },
        { min: 462500, max: 467499, tax: 875 },
        { min: 467500, max: 472499, tax: 1090 },
        { min: 472500, max: 477499, tax: 1306 },
        { min: 477500, max: 482499, tax: 1521 },
        { min: 482500, max: 487499, tax: 1807 },
        { min: 487500, max: 492499, tax: 2238 },
        { min: 492500, max: 497499, tax: 2669 },
        { min: 497500, max: 502499, tax: 3100 },
        { min: 502500, max: 507499, tax: 3531 },
        { min: 507500, max: 512499, tax: 3962 },
        { min: 512500, max: 517499, tax: 4393 },
        { min: 517500, max: 522499, tax: 4824 },
        { min: 522500, max: 527499, tax: 5255 },
        { min: 527500, max: 532499, tax: 5686 },
        { min: 532500, max: 537499, tax: 6259 },
        { min: 537500, max: 542499, tax: 6905 },
        { min: 542500, max: 547499, tax: 7552 },
        { min: 547500, max: 552499, tax: 8198 },
        { min: 552500, max: 557499, tax: 8845 },
        { min: 557500, max: 562499, tax: 9491 },
        { min: 562500, max: 567499, tax: 10138 },
        { min: 567500, max: 572499, tax: 10784 },
        { min: 572500, max: 577499, tax: 11431 },
        { min: 577500, max: 582499, tax: 12077 },
        { min: 582500, max: 587499, tax: 12724 },
        { min: 587500, max: 592499, tax: 13370 },
        { min: 592500, max: 597499, tax: 14017 },
        { min: 597500, max: 602499, tax: 14663 },
        { min: 602500, max: 607499, tax: 15310 },
        { min: 607500, max: 612499, tax: 15956 },
        { min: 612500, max: 617499, tax: 16603 },
        { min: 617500, max: 622499, tax: 17249 },
        { min: 622500, max: 627499, tax: 17896 },
        { min: 627500, max: 632499, tax: 18542 },
        { min: 632500, max: 637499, tax: 19189 },
        { min: 637500, max: 642499, tax: 19835 },
        { min: 642500, max: 647499, tax: 20482 },
        { min: 647500, max: 652499, tax: 21128 },
        { min: 652500, max: 657499, tax: 21775 },
        { min: 657500, max: 662499, tax: 22421 },
        { min: 662500, max: 667499, tax: 23068 },
        { min: 667500, max: 672499, tax: 23714 },
        { min: 672500, max: 677499, tax: 24361 },
        { min: 677500, max: 682499, tax: 25007 },
        { min: 682500, max: 687499, tax: 25654 },
        { min: 687500, max: 692499, tax: 26300 },
        { min: 692500, max: 697499, tax: 26947 },
        { min: 697500, max: 702499, tax: 27593 },
        { min: 702500, max: 707499, tax: 28240 },
        { min: 707500, max: 712499, tax: 28886 },
        { min: 712500, max: 717499, tax: 29533 },
        { min: 717500, max: 722499, tax: 30179 },
        { min: 722500, max: 727499, tax: 30826 },
        { min: 727500, max: 732499, tax: 31472 },
        { min: 732500, max: 737499, tax: 32119 },
        { min: 737500, max: 742499, tax: 32765 },
        { min: 742500, max: 747499, tax: 33412 },
        { min: 747500, max: 752499, tax: 34058 },
        { min: 752500, max: 757499, tax: 34705 },
        { min: 757500, max: 762499, tax: 35351 },
        { min: 762500, max: 767499, tax: 35998 },
        { min: 767500, max: 772499, tax: 36644 },
        { min: 772500, max: 777499, tax: 37291 },
        { min: 777500, max: 782499, tax: 37937 },
        { min: 782500, max: 787499, tax: 38584 },
        { min: 787500, max: 792499, tax: 39230 },
        { min: 792500, max: 797499, tax: 39877 },
        { min: 797500, max: 802499, tax: 40523 },
        { min: 802500, max: 807499, tax: 41170 },
        { min: 807500, max: 812499, tax: 41816 },
        { min: 812500, max: 817499, tax: 42463 },
        { min: 817500, max: 822499, tax: 43109 },
        { min: 822500, max: 827499, tax: 43756 },
        { min: 827500, max: 832499, tax: 44402 },
        { min: 832500, max: 837499, tax: 45049 },
        { min: 837500, max: 842499, tax: 45695 },
        { min: 842500, max: 847499, tax: 46342 },
        { min: 847500, max: 852499, tax: 46988 },
        { min: 852500, max: 857499, tax: 47635 },
        { min: 857500, max: 862499, tax: 48281 },
        { min: 862500, max: 867499, tax: 48928 },
        { min: 867500, max: 872499, tax: 49574 },
        { min: 872500, max: 877499, tax: 50221 },
        { min: 877500, max: 882499, tax: 50867 },
        { min: 882500, max: 887499, tax: 51514 },
        { min: 887500, max: 892499, tax: 52160 },
        { min: 892500, max: 897499, tax: 52807 },
        { min: 897500, max: 902499, tax: 53453 },
        { min: 902500, max: 907499, tax: 54100 },
        { min: 907500, max: 912499, tax: 54746 },
        { min: 912500, max: 917499, tax: 55393 },
        { min: 917500, max: 922499, tax: 56039 },
        { min: 922500, max: 927499, tax: 56686 },
        { min: 927500, max: 932499, tax: 57332 },
        { min: 932500, max: 937499, tax: 57979 },
        { min: 937500, max: 942499, tax: 58625 },
        { min: 942500, max: 947499, tax: 59272 },
        { min: 947500, max: 952499, tax: 59918 },
        { min: 952500, max: 957499, tax: 60565 },
        { min: 957500, max: 962499, tax: 61211 },
        { min: 962500, max: 967499, tax: 61858 },
        { min: 967500, max: 972499, tax: 62504 },
        { min: 972500, max: 977499, tax: 63151 },
        { min: 977500, max: 982499, tax: 63797 },
        { min: 982500, max: 987499, tax: 64444 },
        { min: 987500, max: 992499, tax: 65090 },
        { min: 992500, max: 997499, tax: 65737 },
        { min: 997500, max: 1002499, tax: 66383 },
        { min: 1002500, max: 1007499, tax: 67030 },
        { min: 1007500, max: 1012499, tax: 67676 },
        { min: 1012500, max: 1017499, tax: 68323 },
        { min: 1017500, max: 1022499, tax: 68969 },
        { min: 1022500, max: 1027499, tax: 69616 },
        { min: 1027500, max: 1032499, tax: 70262 },
        { min: 1032500, max: 1037499, tax: 70909 },
        { min: 1037500, max: 1042499, tax: 71555 },
        { min: 1042500, max: 1047499, tax: 72202 },
        { min: 1047500, max: 1052499, tax: 72848 },
        { min: 1052500, max: 1057499, tax: 73495 },
        { min: 1057500, max: 1062499, tax: 74141 },
        { min: 1062500, max: 1067499, tax: 74788 },
        { min: 1067500, max: 1072499, tax: 75434 },
        { min: 1072500, max: 1077499, tax: 76081 },
        { min: 1077500, max: 1082499, tax: 76727 },
        { min: 1082500, max: 1087499, tax: 77374 },
        { min: 1087500, max: 1092499, tax: 78020 },
        { min: 1092500, max: 1097499, tax: 78667 },
        { min: 1097500, max: 1102499, tax: 79313 },
        { min: 1102500, max: 1107499, tax: 79960 },
        { min: 1107500, max: 1112499, tax: 80606 },
        { min: 1112500, max: 1117499, tax: 81253 },
        { min: 1117500, max: 1122499, tax: 81899 },
        { min: 1122500, max: 1127499, tax: 82546 },
        { min: 1127500, max: 1132499, tax: 83192 },
        { min: 1132500, max: 1137499, tax: 83839 },
        { min: 1137500, max: 1142499, tax: 84485 },
        { min: 1142500, max: 1147499, tax: 85132 },
        { min: 1147500, max: 1152499, tax: 85778 },
        { min: 1152500, max: 1157499, tax: 86425 },
        { min: 1157500, max: 1162499, tax: 87071 },
        { min: 1162500, max: 1167499, tax: 87718 },
        { min: 1167500, max: 1172499, tax: 88364 },
        { min: 1172500, max: 1177499, tax: 89011 },
        { min: 1177500, max: 1182499, tax: 89657 },
        { min: 1182500, max: 1187499, tax: 90304 },
        { min: 1187500, max: 1192499, tax: 90950 },
        { min: 1192500, max: 1197499, tax: 91597 },
        { min: 1197500, max: 1202499, tax: 92243 },
        { min: 1202500, max: 1207499, tax: 92890 },
        { min: 1207500, max: 1212499, tax: 93536 },
        { min: 1212500, max: 1217499, tax: 94183 },
        { min: 1217500, max: 1222499, tax: 94829 },
        { min: 1222500, max: 1227499, tax: 95476 },
        { min: 1227500, max: 1232499, tax: 96122 },
        { min: 1232500, max: 1237499, tax: 96769 },
        { min: 1237500, max: 1242499, tax: 97415 },
        { min: 1242500, max: 1247499, tax: 98062 },
        { min: 1247500, max: 1252499, tax: 98708 },
        { min: 1252500, max: 1257499, tax: 99355 },
        { min: 1257500, max: 1262499, tax: 100001 },
        { min: 1262500, max: 1267499, tax: 100648 },
        { min: 1267500, max: 1272499, tax: 101294 },
        { min: 1272500, max: 1277499, tax: 101941 },
        { min: 1277500, max: 1282499, tax: 102587 },
        { min: 1282500, max: 1287499, tax: 103234 },
        { min: 1287500, max: 1292499, tax: 103880 },
        { min: 1292500, max: 1297499, tax: 104527 },
        { min: 1297500, max: 1302499, tax: 105173 },
        { min: 1302500, max: 1307499, tax: 105820 },
        { min: 1307500, max: 1312499, tax: 106466 },
        { min: 1312500, max: 1317499, tax: 107113 },
        { min: 1317500, max: 1322499, tax: 107759 },
        { min: 1322500, max: 1327499, tax: 108406 },
        { min: 1327500, max: 1332499, tax: 109052 },
        { min: 1332500, max: 1337499, tax: 109699 },
        { min: 1337500, max: 1342499, tax: 110345 },
        { min: 1342500, max: 1347499, tax: 110992 },
        { min: 1347500, max: 1352499, tax: 111638 },
        { min: 1352500, max: 1357499, tax: 112285 },
        { min: 1357500, max: 1362499, tax: 112931 },
        { min: 1362500, max: 1367499, tax: 113578 },
        { min: 1367500, max: 1372499, tax: 114224 },
        { min: 1372500, max: 1377499, tax: 114871 },
        { min: 1377500, max: 1382499, tax: 115517 },
        { min: 1382500, max: 1387499, tax: 116164 },
        { min: 1387500, max: 1392499, tax: 116810 },
        { min: 1392500, max: 1397499, tax: 117457 },
        { min: 1397500, max: 1402499, tax: 118103 },
        { min: 1402500, max: 1407499, tax: 118750 },
        { min: 1407500, max: 1412499, tax: 119396 },
        { min: 1412500, max: 1417499, tax: 120043 },
        { min: 1417500, max: 1422499, tax: 120689 },
        { min: 1422500, max: 1427499, tax: 121336 },
        { min: 1427500, max: 1432499, tax: 121982 },
        { min: 1432500, max: 1437499, tax: 122629 },
        { min: 1437500, max: 1442499, tax: 123275 },
        { min: 1442500, max: 1447499, tax: 123922 },
        { min: 1447500, max: 1452499, tax: 124568 },
        { min: 1452500, max: 1457499, tax: 125215 },
        { min: 1457500, max: 1462499, tax: 125861 },
        { min: 1462500, max: 1467499, tax: 126508 },
        { min: 1467500, max: 1472499, tax: 127154 },
        { min: 1472500, max: 1477499, tax: 127801 },
        { min: 1477500, max: 1482499, tax: 128447 },
        { min: 1482500, max: 1487499, tax: 129094 },
        { min: 1487500, max: 1492499, tax: 129740 },
        { min: 1492500, max: 1497499, tax: 130387 },
        { min: 1497500, max: 1502499, tax: 131033 }
    ],
    "1": [ // طفل واحد
        { min: 242500, max: 247499, tax: 0 },
        { min: 247500, max: 252499, tax: 0 },
        { min: 252500, max: 257499, tax: 0 },
        { min: 257500, max: 262499, tax: 0 },
        { min: 262500, max: 267499, tax: 0 },
        { min: 267500, max: 272499, tax: 0 },
        { min: 272500, max: 277499, tax: 0 },
        { min: 277500, max: 282499, tax: 0 },
        { min: 282500, max: 287499, tax: 0 },
        { min: 287500, max: 292499, tax: 0 },
        { min: 292500, max: 297499, tax: 0 },
        { min: 297500, max: 302499, tax: 0 },
        { min: 302500, max: 307499, tax: 0 },
        { min: 307500, max: 312499, tax: 0 },
        { min: 312500, max: 317499, tax: 0 },
        { min: 317500, max: 322499, tax: 0 },
        { min: 322500, max: 327499, tax: 0 },
        { min: 327500, max: 332499, tax: 0 },
        { min: 332500, max: 337499, tax: 0 },
        { min: 337500, max: 342499, tax: 0 },
        { min: 342500, max: 347499, tax: 0 },
        { min: 347500, max: 352499, tax: 0 },
        { min: 352500, max: 357499, tax: 0 },
        { min: 357500, max: 362499, tax: 0 },
        { min: 362500, max: 367499, tax: 0 },
        { min: 367500, max: 372499, tax: 0 },
        { min: 372500, max: 377499, tax: 0 },
        { min: 377500, max: 382499, tax: 0 },
        { min: 382500, max: 387499, tax: 0 },
        { min: 387500, max: 392499, tax: 0 },
        { min: 392500, max: 397499, tax: 0 },
        { min: 397500, max: 402499, tax: 0 },
        { min: 402500, max: 407499, tax: 0 },
        { min: 407500, max: 412499, tax: 0 },
        { min: 412500, max: 417499, tax: 0 },
        { min: 417500, max: 422499, tax: 0 },
        { min: 422500, max: 427499, tax: 0 },
        { min: 427500, max: 432499, tax: 0 },
        { min: 432500, max: 437499, tax: 0 },
        { min: 437500, max: 442499, tax: 0 },
        { min: 442500, max: 447499, tax: 0 },
        { min: 447500, max: 452499, tax: 0 },
        { min: 452500, max: 457499, tax: 16 },
        { min: 457500, max: 462499, tax: 146 },
        { min: 462500, max: 467499, tax: 275 },
        { min: 467500, max: 472499, tax: 404 },
        { min: 472500, max: 477499, tax: 534 },
        { min: 477500, max: 482499, tax: 688 },
        { min: 482500, max: 487499, tax: 904 },
        { min: 487500, max: 492499, tax: 1119 },
        { min: 492500, max: 497499, tax: 1335 },
        { min: 497500, max: 502499, tax: 1550 },
        { min: 502500, max: 507499, tax: 1864 },
        { min: 507500, max: 512499, tax: 2295 },
        { min: 512500, max: 517499, tax: 2726 },
        { min: 517500, max: 522499, tax: 3157 },
        { min: 522500, max: 527499, tax: 3588 },
        { min: 527500, max: 532499, tax: 4019 },
        { min: 532500, max: 537499, tax: 4450 },
        { min: 537500, max: 542499, tax: 4881 },
        { min: 542500, max: 547499, tax: 5312 },
        { min: 547500, max: 552499, tax: 5743 },
        { min: 552500, max: 557499, tax: 6345 },
        { min: 557500, max: 562499, tax: 6991 },
        { min: 562500, max: 567499, tax: 7638 },
        { min: 567500, max: 572499, tax: 8284 },
        { min: 572500, max: 577499, tax: 8931 },
        { min: 577500, max: 582499, tax: 9577 },
        { min: 582500, max: 587499, tax: 10224 },
        { min: 587500, max: 592499, tax: 10870 },
        { min: 592500, max: 597499, tax: 11517 },
        { min: 597500, max: 602499, tax: 12163 },
        { min: 602500, max: 607499, tax: 12810 },
        { min: 607500, max: 612499, tax: 13456 },
        { min: 612500, max: 617499, tax: 14103 },
        { min: 617500, max: 622499, tax: 14749 },
        { min: 622500, max: 627499, tax: 15396 },
        { min: 627500, max: 632499, tax: 16042 },
        { min: 632500, max: 637499, tax: 16689 },
        { min: 637500, max: 642499, tax: 17335 },
        { min: 642500, max: 647499, tax: 17982 },
        { min: 647500, max: 652499, tax: 18628 },
        { min: 652500, max: 657499, tax: 19275 },
        { min: 657500, max: 662499, tax: 19921 },
        { min: 662500, max: 667499, tax: 20568 },
        { min: 667500, max: 672499, tax: 21214 },
        { min: 672500, max: 677499, tax: 21861 },
        { min: 677500, max: 682499, tax: 22507 },
        { min: 682500, max: 687499, tax: 23154 },
        { min: 687500, max: 692499, tax: 23800 },
        { min: 692500, max: 697499, tax: 24447 },
        { min: 697500, max: 702499, tax: 25093 },
        { min: 702500, max: 707499, tax: 25740 },
        { min: 707500, max: 712499, tax: 26386 },
        { min: 712500, max: 717499, tax: 27033 },
        { min: 717500, max: 722499, tax: 27679 },
        { min: 722500, max: 727499, tax: 28326 },
        { min: 727500, max: 732499, tax: 28972 },
        { min: 732500, max: 737499, tax: 29619 },
        { min: 737500, max: 742499, tax: 30265 },
        { min: 742500, max: 747499, tax: 30912 },
        { min: 747500, max: 752499, tax: 31558 },
        { min: 752500, max: 757499, tax: 32205 },
        { min: 757500, max: 762499, tax: 32851 },
        { min: 762500, max: 767499, tax: 33498 },
        { min: 767500, max: 772499, tax: 34144 },
        { min: 772500, max: 777499, tax: 34791 },
        { min: 777500, max: 782499, tax: 35437 },
        { min: 782500, max: 787499, tax: 36084 },
        { min: 787500, max: 792499, tax: 36730 },
        { min: 792500, max: 797499, tax: 37377 },
        { min: 797500, max: 802499, tax: 38023 },
        { min: 802500, max: 807499, tax: 38670 },
        { min: 807500, max: 812499, tax: 39316 },
        { min: 812500, max: 817499, tax: 39963 },
        { min: 817500, max: 822499, tax: 40609 },
        { min: 822500, max: 827499, tax: 41256 },
        { min: 827500, max: 832499, tax: 41902 },
        { min: 832500, max: 837499, tax: 42549 },
        { min: 837500, max: 842499, tax: 43195 },
        { min: 842500, max: 847499, tax: 43842 },
        { min: 847500, max: 852499, tax: 44488 },
        { min: 852500, max: 857499, tax: 45135 },
        { min: 857500, max: 862499, tax: 45781 },
        { min: 862500, max: 867499, tax: 46428 },
        { min: 867500, max: 872499, tax: 47074 },
        { min: 872500, max: 877499, tax: 47721 },
        { min: 877500, max: 882499, tax: 48367 },
        { min: 882500, max: 887499, tax: 49014 },
        { min: 887500, max: 892499, tax: 49660 },
        { min: 892500, max: 897499, tax: 50307 },
        { min: 897500, max: 902499, tax: 50953 },
        { min: 902500, max: 907499, tax: 51600 },
        { min: 907500, max: 912499, tax: 52246 },
        { min: 912500, max: 917499, tax: 52893 },
        { min: 917500, max: 922499, tax: 53539 },
        { min: 922500, max: 927499, tax: 54186 },
        { min: 927500, max: 932499, tax: 54832 },
        { min: 932500, max: 937499, tax: 55479 },
        { min: 937500, max: 942499, tax: 56125 },
        { min: 942500, max: 947499, tax: 56772 },
        { min: 947500, max: 952499, tax: 57418 },
        { min: 952500, max: 957499, tax: 58065 },
        { min: 957500, max: 962499, tax: 58711 },
        { min: 962500, max: 967499, tax: 59358 },
        { min: 967500, max: 972499, tax: 60004 },
        { min: 972500, max: 977499, tax: 60651 },
        { min: 977500, max: 982499, tax: 61297 },
        { min: 982500, max: 987499, tax: 61944 },
        { min: 987500, max: 992499, tax: 62590 },
        { min: 992500, max: 997499, tax: 63237 },
        { min: 997500, max: 1002499, tax: 63883 },
        { min: 1002500, max: 1007499, tax: 64530 },
        { min: 1007500, max: 1012499, tax: 65176 },
        { min: 1012500, max: 1017499, tax: 65823 },
        { min: 1017500, max: 1022499, tax: 66469 },
        { min: 1022500, max: 1027499, tax: 67116 },
        { min: 1027500, max: 1032499, tax: 67762 },
        { min: 1032500, max: 1037499, tax: 68409 },
        { min: 1037500, max: 1042499, tax: 69055 },
        { min: 1042500, max: 1047499, tax: 69702 },
        { min: 1047500, max: 1052499, tax: 70348 },
        { min: 1052500, max: 1057499, tax: 70995 },
        { min: 1057500, max: 1062499, tax: 71641 },
        { min: 1062500, max: 1067499, tax: 72288 },
        { min: 1067500, max: 1072499, tax: 72934 },
        { min: 1072500, max: 1077499, tax: 73581 },
        { min: 1077500, max: 1082499, tax: 74227 },
        { min: 1082500, max: 1087499, tax: 74874 },
        { min: 1087500, max: 1092499, tax: 75520 },
        { min: 1092500, max: 1097499, tax: 76167 },
        { min: 1097500, max: 1102499, tax: 76813 },
        { min: 1102500, max: 1107499, tax: 77460 },
        { min: 1107500, max: 1112499, tax: 78106 },
        { min: 1112500, max: 1117499, tax: 78753 },
        { min: 1117500, max: 1122499, tax: 79399 },
        { min: 1122500, max: 1127499, tax: 80046 },
        { min: 1127500, max: 1132499, tax: 80692 },
        { min: 1132500, max: 1137499, tax: 81339 },
        { min: 1137500, max: 1142499, tax: 81985 },
        { min: 1142500, max: 1147499, tax: 82632 },
        { min: 1147500, max: 1152499, tax: 83278 },
        { min: 1152500, max: 1157499, tax: 83925 },
        { min: 1157500, max: 1162499, tax: 84571 },
        { min: 1162500, max: 1167499, tax: 85218 },
        { min: 1167500, max: 1172499, tax: 85864 },
        { min: 1172500, max: 1177499, tax: 86511 },
        { min: 1177500, max: 1182499, tax: 87157 },
        { min: 1182500, max: 1187499, tax: 87804 },
        { min: 1187500, max: 1192499, tax: 88450 },
        { min: 1192500, max: 1197499, tax: 89097 },
        { min: 1197500, max: 1202499, tax: 89743 },
        { min: 1202500, max: 1207499, tax: 90390 },
        { min: 1207500, max: 1212499, tax: 91036 },
        { min: 1212500, max: 1217499, tax: 91683 },
        { min: 1217500, max: 1222499, tax: 92329 },
        { min: 1222500, max: 1227499, tax: 92976 },
        { min: 1227500, max: 1232499, tax: 93622 },
        { min: 1232500, max: 1237499, tax: 94269 },
        { min: 1237500, max: 1242499, tax: 94915 },
        { min: 1242500, max: 1247499, tax: 95562 },
        { min: 1247500, max: 1252499, tax: 96208 },
        { min: 1252500, max: 1257499, tax: 96855 },
        { min: 1257500, max: 1262499, tax: 97501 },
        { min: 1262500, max: 1267499, tax: 98148 },
        { min: 1267500, max: 1272499, tax: 98794 },
        { min: 1272500, max: 1277499, tax: 99441 },
        { min: 1277500, max: 1282499, tax: 100087 },
        { min: 1282500, max: 1287499, tax: 100734 },
        { min: 1287500, max: 1292499, tax: 101380 },
        { min: 1292500, max: 1297499, tax: 102027 },
        { min: 1297500, max: 1302499, tax: 102673 },
        { min: 1302500, max: 1307499, tax: 103320 },
        { min: 1307500, max: 1312499, tax: 103966 },
        { min: 1312500, max: 1317499, tax: 104613 },
        { min: 1317500, max: 1322499, tax: 105259 },
        { min: 1322500, max: 1327499, tax: 105906 },
        { min: 1327500, max: 1332499, tax: 106552 },
        { min: 1332500, max: 1337499, tax: 107199 },
        { min: 1337500, max: 1342499, tax: 107845 },
        { min: 1342500, max: 1347499, tax: 108492 },
        { min: 1347500, max: 1352499, tax: 109138 },
        { min: 1352500, max: 1357499, tax: 109785 },
        { min: 1357500, max: 1362499, tax: 110431 },
        { min: 1362500, max: 1367499, tax: 111078 },
        { min: 1367500, max: 1372499, tax: 111724 },
        { min: 1372500, max: 1377499, tax: 112371 },
        { min: 1377500, max: 1382499, tax: 113017 },
        { min: 1382500, max: 1387499, tax: 113664 },
        { min: 1387500, max: 1392499, tax: 114310 },
        { min: 1392500, max: 1397499, tax: 114957 },
        { min: 1397500, max: 1402499, tax: 115603 },
        { min: 1402500, max: 1407499, tax: 116250 },
        { min: 1407500, max: 1412499, tax: 116896 },
        { min: 1412500, max: 1417499, tax: 117543 },
        { min: 1417500, max: 1422499, tax: 118189 },
        { min: 1422500, max: 1427499, tax: 118836 },
        { min: 1427500, max: 1432499, tax: 119482 },
        { min: 1432500, max: 1437499, tax: 120129 },
        { min: 1437500, max: 1442499, tax: 120775 },
        { min: 1442500, max: 1447499, tax: 121422 },
        { min: 1447500, max: 1452499, tax: 122068 },
        { min: 1452500, max: 1457499, tax: 122715 },
        { min: 1457500, max: 1462499, tax: 123361 },
        { min: 1462500, max: 1467499, tax: 124008 },
        { min: 1467500, max: 1472499, tax: 124654 },
        { min: 1472500, max: 1477499, tax: 125301 },
        { min: 1477500, max: 1482499, tax: 125947 },
        { min: 1482500, max: 1487499, tax: 126594 },
        { min: 1487500, max: 1492499, tax: 127240 },
        { min: 1492500, max: 1497499, tax: 127887 },
        { min: 1497500, max: 1502499, tax: 128533 }
    ],
    "2": [ // طفلان
        { min: 242500, max: 247499, tax: 0 },
        { min: 247500, max: 252499, tax: 0 },
        { min: 252500, max: 257499, tax: 0 },
        { min: 257500, max: 262499, tax: 0 },
        { min: 262500, max: 267499, tax: 0 },
        { min: 267500, max: 272499, tax: 0 },
        { min: 272500, max: 277499, tax: 0 },
        { min: 277500, max: 282499, tax: 0 },
        { min: 282500, max: 287499, tax: 0 },
        { min: 287500, max: 292499, tax: 0 },
        { min: 292500, max: 297499, tax: 0 },
        { min: 297500, max: 302499, tax: 0 },
        { min: 302500, max: 307499, tax: 0 },
        { min: 307500, max: 312499, tax: 0 },
        { min: 312500, max: 317499, tax: 0 },
        { min: 317500, max: 322499, tax: 0 },
        { min: 322500, max: 327499, tax: 0 },
        { min: 327500, max: 332499, tax: 0 },
        { min: 332500, max: 337499, tax: 0 },
        { min: 337500, max: 342499, tax: 0 },
        { min: 342500, max: 347499, tax: 0 },
        { min: 347500, max: 352499, tax: 0 },
        { min: 352500, max: 357499, tax: 0 },
        { min: 357500, max: 362499, tax: 0 },
        { min: 362500, max: 367499, tax: 0 },
        { min: 367500, max: 372499, tax: 0 },
        { min: 372500, max: 377499, tax: 0 },
        { min: 377500, max: 382499, tax: 0 },
        { min: 382500, max: 387499, tax: 0 },
        { min: 387500, max: 392499, tax: 0 },
        { min: 392500, max: 397499, tax: 0 },
        { min: 397500, max: 402499, tax: 0 },
        { min: 402500, max: 407499, tax: 0 },
        { min: 407500, max: 412499, tax: 0 },
        { min: 412500, max: 417499, tax: 0 },
        { min: 417500, max: 422499, tax: 0 },
        { min: 422500, max: 427499, tax: 0 },
        { min: 427500, max: 432499, tax: 0 },
        { min: 432500, max: 437499, tax: 0 },
        { min: 437500, max: 442499, tax: 0 },
        { min: 442500, max: 447499, tax: 0 },
        { min: 447500, max: 452499, tax: 0 },
        { min: 452500, max: 457499, tax: 0 },
        { min: 457500, max: 462499, tax: 0 },
        { min: 462500, max: 467499, tax: 0 },
        { min: 467500, max: 472499, tax: 0 },
        { min: 472500, max: 477499, tax: 34 },
        { min: 477500, max: 482499, tax: 163 },
        { min: 482500, max: 487499, tax: 292 },
        { min: 487500, max: 492499, tax: 421 },
        { min: 492500, max: 497499, tax: 551 },
        { min: 497500, max: 502499, tax: 717 },
        { min: 502500, max: 507499, tax: 932 },
        { min: 507500, max: 512499, tax: 1148 },
        { min: 512500, max: 517499, tax: 1363 },
        { min: 517500, max: 522499, tax: 1579 },
        { min: 522500, max: 527499, tax: 1922 },
        { min: 527500, max: 532499, tax: 2353 },
        { min: 532500, max: 537499, tax: 2784 },
        { min: 537500, max: 542499, tax: 3215 },
        { min: 542500, max: 547499, tax: 3646 },
        { min: 547500, max: 552499, tax: 4077 },
        { min: 552500, max: 557499, tax: 4508 },
        { min: 557500, max: 562499, tax: 4939 },
        { min: 562500, max: 567499, tax: 5370 },
        { min: 567500, max: 572499, tax: 5801 },
        { min: 572500, max: 577499, tax: 6431 },
        { min: 577500, max: 582499, tax: 7077 },
        { min: 582500, max: 587499, tax: 7724 },
        { min: 587500, max: 592499, tax: 8370 },
        { min: 592500, max: 597499, tax: 9017 },
        { min: 597500, max: 602499, tax: 9663 },
        { min: 602500, max: 607499, tax: 10310 },
        { min: 607500, max: 612499, tax: 10956 },
        { min: 612500, max: 617499, tax: 11603 },
        { min: 617500, max: 622499, tax: 12249 },
        { min: 622500, max: 627499, tax: 12896 },
        { min: 627500, max: 632499, tax: 13542 },
        { min: 632500, max: 637499, tax: 14189 },
        { min: 637500, max: 642499, tax: 14835 },
        { min: 642500, max: 647499, tax: 15482 },
        { min: 647500, max: 652499, tax: 16128 },
        { min: 652500, max: 657499, tax: 16775 },
        { min: 657500, max: 662499, tax: 17421 },
        { min: 662500, max: 667499, tax: 18068 },
        { min: 667500, max: 672499, tax: 18714 },
        { min: 672500, max: 677499, tax: 19361 },
        { min: 677500, max: 682499, tax: 26007 },
        { min: 682500, max: 687499, tax: 20654 },
        { min: 687500, max: 692499, tax: 21300 },
        { min: 692500, max: 697499, tax: 21947 },
        { min: 697500, max: 702499, tax: 22593 },
        { min: 702500, max: 707499, tax: 23240 },
        { min: 707500, max: 712499, tax: 23886 },
        { min: 712500, max: 717499, tax: 24533 },
        { min: 717500, max: 722499, tax: 25179 },
        { min: 722500, max: 727499, tax: 25826 },
        { min: 727500, max: 732499, tax: 26472 },
        { min: 732500, max: 737499, tax: 27119 },
        { min: 737500, max: 742499, tax: 27765 },
        { min: 742500, max: 747499, tax: 28412 },
        { min: 747500, max: 752499, tax: 29058 },
        { min: 752500, max: 757499, tax: 29705 },
        { min: 757500, max: 762499, tax: 30351 },
        { min: 762500, max: 767499, tax: 30998 },
        { min: 767500, max: 772499, tax: 31644 },
        { min: 772500, max: 777499, tax: 32291 },
        { min: 777500, max: 782499, tax: 32937 },
        { min: 782500, max: 787499, tax: 33584 },
        { min: 787500, max: 792499, tax: 34230 },
        { min: 792500, max: 797499, tax: 34877 },
        { min: 797500, max: 802499, tax: 35523 },
        { min: 802500, max: 807499, tax: 36170 },
        { min: 807500, max: 812499, tax: 36816 },
        { min: 812500, max: 817499, tax: 37463 },
        { min: 817500, max: 822499, tax: 38109 },
        { min: 822500, max: 827499, tax: 38756 },
        { min: 827500, max: 832499, tax: 39402 },
        { min: 832500, max: 837499, tax: 40049 },
        { min: 837500, max: 842499, tax: 40695 },
        { min: 842500, max: 847499, tax: 41342 },
        { min: 847500, max: 852499, tax: 41988 },
        { min: 852500, max: 857499, tax: 42635 },
        { min: 857500, max: 862499, tax: 43281 },
        { min: 862500, max: 867499, tax: 43928 },
        { min: 867500, max: 872499, tax: 44574 },
        { min: 872500, max: 877499, tax: 45221 },
        { min: 877500, max: 882499, tax: 45867 },
        { min: 882500, max: 887499, tax: 46514 },
        { min: 887500, max: 892499, tax: 47160 },
        { min: 892500, max: 897499, tax: 47807 },
        { min: 897500, max: 902499, tax: 48453 },
        { min: 902500, max: 907499, tax: 49100 },
        { min: 907500, max: 912499, tax: 49746 },
        { min: 912500, max: 917499, tax: 50393 },
        { min: 917500, max: 922499, tax: 51039 },
        { min: 922500, max: 927499, tax: 51686 },
        { min: 927500, max: 932499, tax: 52332 },
        { min: 932500, max: 937499, tax: 52979 },
        { min: 937500, max: 942499, tax: 53625 },
        { min: 942500, max: 947499, tax: 54272 },
        { min: 947500, max: 952499, tax: 54918 },
        { min: 952500, max: 957499, tax: 55565 },
        { min: 957500, max: 962499, tax: 56211 },
        { min: 962500, max: 967499, tax: 56858 },
        { min: 967500, max: 972499, tax: 57504 },
        { min: 972500, max: 977499, tax: 58151 },
        { min: 977500, max: 982499, tax: 58797 },
        { min: 982500, max: 987499, tax: 59444 },
        { min: 987500, max: 992499, tax: 60090 },
        { min: 992500, max: 997499, tax: 60737 },
        { min: 997500, max: 1002499, tax: 61383 },
        { min: 1002500, max: 1007499, tax: 62030 },
        { min: 1007500, max: 1012499, tax: 62676 },
        { min: 1012500, max: 1017499, tax: 63323 },
        { min: 1017500, max: 1022499, tax: 63969 },
        { min: 1022500, max: 1027499, tax: 64616 },
        { min: 1027500, max: 1032499, tax: 65262 },
        { min: 1032500, max: 1037499, tax: 65909 },
        { min: 1037500, max: 1042499, tax: 66555 },
        { min: 1042500, max: 1047499, tax: 67202 },
        { min: 1047500, max: 1052499, tax: 67848 },
        { min: 1052500, max: 1057499, tax: 68495 },
        { min: 1057500, max: 1062499, tax: 69141 },
        { min: 1062500, max: 1067499, tax: 69788 },
        { min: 1067500, max: 1072499, tax: 70434 },
        { min: 1072500, max: 1077499, tax: 71081 },
        { min: 1077500, max: 1082499, tax: 71727 },
        { min: 1082500, max: 1087499, tax: 72374 },
        { min: 1087500, max: 1092499, tax: 73020 },
        { min: 1092500, max: 1097499, tax: 73667 },
        { min: 1097500, max: 1102499, tax: 74313 },
        { min: 1102500, max: 1107499, tax: 74960 },
        { min: 1107500, max: 1112499, tax: 75606 },
        { min: 1112500, max: 1117499, tax: 76253 },
        { min: 1117500, max: 1122499, tax: 76899 },
        { min: 1122500, max: 1127499, tax: 77546 },
        { min: 1127500, max: 1132499, tax: 78192 },
        { min: 1132500, max: 1137499, tax: 78839 },
        { min: 1137500, max: 1142499, tax: 79485 },
        { min: 1142500, max: 1147499, tax: 80132 },
        { min: 1147500, max: 1152499, tax: 80778 },
        { min: 1152500, max: 1157499, tax: 81425 },
        { min: 1157500, max: 1162499, tax: 82071 },
        { min: 1162500, max: 1167499, tax: 82718 },
        { min: 1167500, max: 1172499, tax: 83364 },
        { min: 1172500, max: 1177499, tax: 84011 },
        { min: 1177500, max: 1182499, tax: 84657 },
        { min: 1182500, max: 1187499, tax: 85304 },
        { min: 1187500, max: 1192499, tax: 85950 },
        { min: 1192500, max: 1197499, tax: 86597 },
        { min: 1197500, max: 1202499, tax: 87243 },
        { min: 1202500, max: 1207499, tax: 87890 },
        { min: 1207500, max: 1212499, tax: 88536 },
        { min: 1212500, max: 1217499, tax: 89183 },
        { min: 1217500, max: 1222499, tax: 89829 },
        { min: 1222500, max: 1227499, tax: 90476 },
        { min: 1227500, max: 1232499, tax: 91122 },
        { min: 1232500, max: 1237499, tax: 91769 },
        { min: 1237500, max: 1242499, tax: 92415 },
        { min: 1242500, max: 1247499, tax: 93062 },
        { min: 1247500, max: 1252499, tax: 93708 },
        { min: 1252500, max: 1257499, tax: 94355 },
        { min: 1257500, max: 1262499, tax: 95001 },
        { min: 1262500, max: 1267499, tax: 95648 },
        { min: 1267500, max: 1272499, tax: 96294 },
        { min: 1272500, max: 1277499, tax: 96941 },
        { min: 1277500, max: 1282499, tax: 97587 },
        { min: 1282500, max: 1287499, tax: 98234 },
        { min: 1287500, max: 1292499, tax: 98880 },
        { min: 1292500, max: 1297499, tax: 99527 },
        { min: 1297500, max: 1302499, tax: 100173 },
        { min: 1302500, max: 1307499, tax: 100820 },
        { min: 1307500, max: 1312499, tax: 101466 },
        { min: 1312500, max: 1317499, tax: 102113 },
        { min: 1317500, max: 1322499, tax: 102759 },
        { min: 1322500, max: 1327499, tax: 103406 },
        { min: 1327500, max: 1332499, tax: 104052 },
        { min: 1332500, max: 1337499, tax: 104699 },
        { min: 1337500, max: 1342499, tax: 105345 },
        { min: 1342500, max: 1347499, tax: 105992 },
        { min: 1347500, max: 1352499, tax: 106638 },
        { min: 1352500, max: 1357499, tax: 107285 },
        { min: 1357500, max: 1362499, tax: 107931 },
        { min: 1362500, max: 1367499, tax: 108578 },
        { min: 1367500, max: 1372499, tax: 109224 },
        { min: 1372500, max: 1377499, tax: 109871 },
        { min: 1377500, max: 1382499, tax: 110517 },
        { min: 1382500, max: 1387499, tax: 111164 },
        { min: 1387500, max: 1392499, tax: 111810 },
        { min: 1392500, max: 1397499, tax: 112457 },
        { min: 1397500, max: 1402499, tax: 113103 },
        { min: 1402500, max: 1407499, tax: 113750 },
        { min: 1407500, max: 1412499, tax: 114396 },
        { min: 1412500, max: 1417499, tax: 115043 },
        { min: 1417500, max: 1422499, tax: 115689 },
        { min: 1422500, max: 1427499, tax: 116336 },
        { min: 1427500, max: 1432499, tax: 116982 },
        { min: 1432500, max: 1437499, tax: 117629 },
        { min: 1437500, max: 1442499, tax: 118275 },
        { min: 1442500, max: 1447499, tax: 118922 },
        { min: 1447500, max: 1452499, tax: 119568 },
        { min: 1452500, max: 1457499, tax: 120215 },
        { min: 1457500, max: 1462499, tax: 120861 },
        { min: 1462500, max: 1467499, tax: 121508 },
        { min: 1467500, max: 1472499, tax: 122154 },
        { min: 1472500, max: 1477499, tax: 122801 },
        { min: 1477500, max: 1482499, tax: 123447 },
        { min: 1482500, max: 1487499, tax: 124094 },
        { min: 1487500, max: 1492499, tax: 124740 },
        { min: 1492500, max: 1497499, tax: 125387 },
        { min: 1497500, max: 1502499, tax: 126033 }
    ],
    "3": [ // 3 أطفال
        { min: 242500, max: 247499, tax: 0 },
        { min: 247500, max: 252499, tax: 0 },
        { min: 252500, max: 257499, tax: 0 },
        { min: 257500, max: 262499, tax: 0 },
        { min: 262500, max: 267499, tax: 0 },
        { min: 267500, max: 272499, tax: 0 },
        { min: 272500, max: 277499, tax: 0 },
        { min: 277500, max: 282499, tax: 0 },
        { min: 282500, max: 287499, tax: 0 },
        { min: 287500, max: 292499, tax: 0 },
        { min: 292500, max: 297499, tax: 0 },
        { min: 297500, max: 302499, tax: 0 },
        { min: 302500, max: 307499, tax: 0 },
        { min: 307500, max: 312499, tax: 0 },
        { min: 312500, max: 317499, tax: 0 },
        { min: 317500, max: 322499, tax: 0 },
        { min: 322500, max: 327499, tax: 0 },
        { min: 327500, max: 332499, tax: 0 },
        { min: 332500, max: 337499, tax: 0 },
        { min: 337500, max: 342499, tax: 0 },
        { min: 342500, max: 347499, tax: 0 },
        { min: 347500, max: 352499, tax: 0 },
        { min: 352500, max: 357499, tax: 0 },
        { min: 357500, max: 362499, tax: 0 },
        { min: 362500, max: 367499, tax: 0 },
        { min: 367500, max: 372499, tax: 0 },
        { min: 372500, max: 377499, tax: 0 },
        { min: 377500, max: 382499, tax: 0 },
        { min: 382500, max: 387499, tax: 0 },
        { min: 387500, max: 392499, tax: 0 },
        { min: 392500, max: 397499, tax: 0 },
        { min: 397500, max: 402499, tax: 0 },
        { min: 402500, max: 407499, tax: 0 },
        { min: 407500, max: 412499, tax: 0 },
        { min: 412500, max: 417499, tax: 0 },
        { min: 417500, max: 422499, tax: 0 },
        { min: 422500, max: 427499, tax: 0 },
        { min: 427500, max: 432499, tax: 0 },
        { min: 432500, max: 437499, tax: 0 },
        { min: 437500, max: 442499, tax: 0 },
        { min: 442500, max: 447499, tax: 0 },
        { min: 447500, max: 452499, tax: 0 },
        { min: 452500, max: 457499, tax: 0 },
        { min: 457500, max: 462499, tax: 0 },
        { min: 462500, max: 467499, tax: 0 },
        { min: 467500, max: 472499, tax: 0 },
        { min: 472500, max: 477499, tax: 0 },
        { min: 477500, max: 482499, tax: 0 },
        { min: 482500, max: 487499, tax: 0 },
        { min: 487500, max: 492499, tax: 0 },
        { min: 492500, max: 497499, tax: 51 },
        { min: 497500, max: 502499, tax: 180 },
        { min: 502500, max: 507499, tax: 309 },
        { min: 507500, max: 512499, tax: 439 },
        { min: 512500, max: 517499, tax: 568 },
        { min: 517500, max: 522499, tax: 745 },
        { min: 522500, max: 527499, tax: 961 },
        { min: 527500, max: 532499, tax: 1176 },
        { min: 532500, max: 537499, tax: 1392 },
        { min: 537500, max: 542499, tax: 1607 },
        { min: 542500, max: 547499, tax: 1979 },
        { min: 547500, max: 552499, tax: 2410 },
        { min: 552500, max: 557499, tax: 2841 },
        { min: 557500, max: 562499, tax: 3272 },
        { min: 562500, max: 567499, tax: 3703 },
        { min: 567500, max: 572499, tax: 4134 },
        { min: 572500, max: 577499, tax: 4565 },
        { min: 577500, max: 582499, tax: 4996 },
        { min: 582500, max: 587499, tax: 5427 },
        { min: 587500, max: 592499, tax: 5870 },
        { min: 592500, max: 597499, tax: 6517 },
        { min: 597500, max: 602499, tax: 7163 },
        { min: 602500, max: 607499, tax: 7810 },
        { min: 607500, max: 612499, tax: 8456 },
        { min: 612500, max: 617499, tax: 9103 },
        { min: 617500, max: 622499, tax: 9749 },
        { min: 622500, max: 627499, tax: 10396 },
        { min: 627500, max: 632499, tax: 11042 },
        { min: 632500, max: 637499, tax: 11689 },
        { min: 637500, max: 642499, tax: 12335 },
        { min: 642500, max: 647499, tax: 12982 },
        { min: 647500, max: 652499, tax: 13628 },
        { min: 652500, max: 657499, tax: 14275 },
        { min: 657500, max: 662499, tax: 14921 },
        { min: 662500, max: 667499, tax: 15568 },
        { min: 667500, max: 672499, tax: 16214 },
        { min: 672500, max: 677499, tax: 16861 },
        { min: 677500, max: 682499, tax: 17507 },
        { min: 682500, max: 687499, tax: 18154 },
        { min: 687500, max: 692499, tax: 18800 },
        { min: 692500, max: 697499, tax: 19447 },
        { min: 697500, max: 702499, tax: 20093 },
        { min: 702500, max: 707499, tax: 20740 },
        { min: 707500, max: 712499, tax: 21386 },
        { min: 712500, max: 717499, tax: 22033 },
        { min: 717500, max: 722499, tax: 22679 },
        { min: 722500, max: 727499, tax: 23326 },
        { min: 727500, max: 732499, tax: 23972 },
        { min: 732500, max: 737499, tax: 24619 },
        { min: 737500, max: 742499, tax: 25265 },
        { min: 742500, max: 747499, tax: 25912 },
        { min: 747500, max: 752499, tax: 26558 },
        { min: 752500, max: 757499, tax: 27205 },
        { min: 757500, max: 762499, tax: 27851 },
        { min: 762500, max: 767499, tax: 28498 },
        { min: 767500, max: 772499, tax: 29144 },
        { min: 772500, max: 777499, tax: 29791 },
        { min: 777500, max: 782499, tax: 30437 },
        { min: 782500, max: 787499, tax: 31084 },
        { min: 787500, max: 792499, tax: 31730 },
        { min: 792500, max: 797499, tax: 32377 },
        { min: 797500, max: 802499, tax: 33023 },
        { min: 802500, max: 807499, tax: 33670 },
        { min: 807500, max: 812499, tax: 34316 },
        { min: 812500, max: 817499, tax: 34963 },
        { min: 817500, max: 822499, tax: 35609 },
        { min: 822500, max: 827499, tax: 36256 },
        { min: 827500, max: 832499, tax: 36902 },
        { min: 832500, max: 837499, tax: 37549 },
        { min: 837500, max: 842499, tax: 38195 },
        { min: 842500, max: 847499, tax: 38842 },
        { min: 847500, max: 852499, tax: 39488 },
        { min: 852500, max: 857499, tax: 40135 },
        { min: 857500, max: 862499, tax: 40781 },
        { min: 862500, max: 867499, tax: 41428 },
        { min: 867500, max: 872499, tax: 42074 },
        { min: 872500, max: 877499, tax: 42721 },
        { min: 877500, max: 882499, tax: 43367 },
        { min: 882500, max: 887499, tax: 44014 },
        { min: 887500, max: 892499, tax: 44660 },
        { min: 892500, max: 897499, tax: 45307 },
        { min: 897500, max: 902499, tax: 45953 },
        { min: 902500, max: 907499, tax: 46600 },
        { min: 907500, max: 912499, tax: 47246 },
        { min: 912500, max: 917499, tax: 47893 },
        { min: 917500, max: 922499, tax: 48539 },
        { min: 922500, max: 927499, tax: 49186 },
        { min: 927500, max: 932499, tax: 49832 },
        { min: 932500, max: 937499, tax: 50479 },
        { min: 937500, max: 942499, tax: 51125 },
        { min: 942500, max: 947499, tax: 51772 },
        { min: 947500, max: 952499, tax: 52418 },
        { min: 952500, max: 957499, tax: 53065 },
        { min: 957500, max: 962499, tax: 53711 },
        { min: 962500, max: 967499, tax: 54358 },
        { min: 967500, max: 972499, tax: 55004 },
        { min: 972500, max: 977499, tax: 55651 },
        { min: 977500, max: 982499, tax: 56297 },
        { min: 982500, max: 987499, tax: 56944 },
        { min: 987500, max: 992499, tax: 57590 },
        { min: 992500, max: 997499, tax: 58237 },
        { min: 997500, max: 1002499, tax: 58883 },
        { min: 1002500, max: 1007499, tax: 59530 },
        { min: 1007500, max: 1012499, tax: 60176 },
        { min: 1012500, max: 1017499, tax: 60823 },
        { min: 1017500, max: 1022499, tax: 61469 },
        { min: 1022500, max: 1027499, tax: 62116 },
        { min: 1027500, max: 1032499, tax: 62762 },
        { min: 1032500, max: 1037499, tax: 63409 },
        { min: 1037500, max: 1042499, tax: 64055 },
        { min: 1042500, max: 1047499, tax: 64702 },
        { min: 1047500, max: 1052499, tax: 65348 },
        { min: 1052500, max: 1057499, tax: 65995 },
        { min: 1057500, max: 1062499, tax: 66641 },
        { min: 1062500, max: 1067499, tax: 67288 },
        { min: 1067500, max: 1072499, tax: 67934 },
        { min: 1072500, max: 1077499, tax: 68581 },
        { min: 1077500, max: 1082499, tax: 69227 },
        { min: 1082500, max: 1087499, tax: 69874 },
        { min: 1087500, max: 1092499, tax: 70520 },
        { min: 1092500, max: 1097499, tax: 71167 },
        { min: 1097500, max: 1102499, tax: 71813 },
        { min: 1102500, max: 1107499, tax: 72460 },
        { min: 1107500, max: 1112499, tax: 73106 },
        { min: 1112500, max: 1117499, tax: 73753 },
        { min: 1117500, max: 1122499, tax: 74399 },
        { min: 1122500, max: 1127499, tax: 75046 },
        { min: 1127500, max: 1132499, tax: 75692 },
        { min: 1132500, max: 1137499, tax: 76339 },
        { min: 1137500, max: 1142499, tax: 76985 },
        { min: 1142500, max: 1147499, tax: 77632 },
        { min: 1147500, max: 1152499, tax: 78278 },
        { min: 1152500, max: 1157499, tax: 78925 },
        { min: 1157500, max: 1162499, tax: 79571 },
        { min: 1162500, max: 1167499, tax: 80218 },
        { min: 1167500, max: 1172499, tax: 80864 },
        { min: 1172500, max: 1177499, tax: 81511 },
        { min: 1177500, max: 1182499, tax: 82157 },
        { min: 1182500, max: 1187499, tax: 82804 },
        { min: 1187500, max: 1192499, tax: 83450 },
        { min: 1192500, max: 1197499, tax: 84097 },
        { min: 1197500, max: 1202499, tax: 84743 },
        { min: 1202500, max: 1207499, tax: 85390 },
        { min: 1207500, max: 1212499, tax: 86036 },
        { min: 1212500, max: 1217499, tax: 86683 },
        { min: 1217500, max: 1222499, tax: 87329 },
        { min: 1222500, max: 1227499, tax: 87976 },
        { min: 1227500, max: 1232499, tax: 88622 },
        { min: 1232500, max: 1237499, tax: 89269 },
        { min: 1237500, max: 1242499, tax: 89915 },
        { min: 1242500, max: 1247499, tax: 90562 },
        { min: 1247500, max: 1252499, tax: 91208 },
        { min: 1252500, max: 1257499, tax: 91855 },
        { min: 1257500, max: 1262499, tax: 92501 },
        { min: 1262500, max: 1267499, tax: 93148 },
        { min: 1267500, max: 1272499, tax: 93794 },
        { min: 1272500, max: 1277499, tax: 94441 },
        { min: 1277500, max: 1282499, tax: 95087 },
        { min: 1282500, max: 1287499, tax: 95734 },
        { min: 1287500, max: 1292499, tax: 96380 },
        { min: 1292500, max: 1297499, tax: 97027 },
        { min: 1297500, max: 1302499, tax: 97673 },
        { min: 1302500, max: 1307499, tax: 98320 },
        { min: 1307500, max: 1312499, tax: 98966 },
        { min: 1312500, max: 1317499, tax: 99613 },
        { min: 1317500, max: 1322499, tax: 100259 },
        { min: 1322500, max: 1327499, tax: 100906 },
        { min: 1327500, max: 1332499, tax: 101552 },
        { min: 1332500, max: 1337499, tax: 102199 },
        { min: 1337500, max: 1342499, tax: 102845 },
        { min: 1342500, max: 1347499, tax: 103492 },
        { min: 1347500, max: 1352499, tax: 104138 },
        { min: 1352500, max: 1357499, tax: 104785 },
        { min: 1357500, max: 1362499, tax: 105431 },
        { min: 1362500, max: 1367499, tax: 106078 },
        { min: 1367500, max: 1372499, tax: 106724 },
        { min: 1372500, max: 1377499, tax: 107371 },
        { min: 1377500, max: 1382499, tax: 108017 },
        { min: 1382500, max: 1387499, tax: 108664 },
        { min: 1387500, max: 1392499, tax: 109310 },
        { min: 1392500, max: 1397499, tax: 109957 },
        { min: 1397500, max: 1402499, tax: 110603 },
        { min: 1402500, max: 1407499, tax: 111250 },
        { min: 1407500, max: 1412499, tax: 111896 },
        { min: 1412500, max: 1417499, tax: 112543 },
        { min: 1417500, max: 1422499, tax: 113189 },
        { min: 1422500, max: 1427499, tax: 113836 },
        { min: 1427500, max: 1432499, tax: 114482 },
        { min: 1432500, max: 1437499, tax: 115129 },
        { min: 1437500, max: 1442499, tax: 115775 },
        { min: 1442500, max: 1447499, tax: 116422 },
        { min: 1447500, max: 1452499, tax: 117068 },
        { min: 1452500, max: 1457499, tax: 117715 },
        { min: 1457500, max: 1462499, tax: 118361 },
        { min: 1462500, max: 1467499, tax: 119008 },
        { min: 1467500, max: 1472499, tax: 119654 },
        { min: 1472500, max: 1477499, tax: 120301 },
        { min: 1477500, max: 1482499, tax: 120947 },
        { min: 1482500, max: 1487499, tax: 121594 },
        { min: 1487500, max: 1492499, tax: 122240 },
        { min: 1492500, max: 1497499, tax: 122887 },
        { min: 1497500, max: 1502499, tax: 123533 }
    ],
    "4": [ // 4 أطفال
        { min: 242500, max: 247499, tax: 0 },
        { min: 247500, max: 252499, tax: 0 },
        { min: 252500, max: 257499, tax: 0 },
        { min: 257500, max: 262499, tax: 0 },
        { min: 262500, max: 267499, tax: 0 },
        { min: 267500, max: 272499, tax: 0 },
        { min: 272500, max: 277499, tax: 0 },
        { min: 277500, max: 282499, tax: 0 },
        { min: 282500, max: 287499, tax: 0 },
        { min: 287500, max: 292499, tax: 0 },
        { min: 292500, max: 297499, tax: 0 },
        { min: 297500, max: 302499, tax: 0 },
        { min: 302500, max: 307499, tax: 0 },
        { min: 307500, max: 312499, tax: 0 },
        { min: 312500, max: 317499, tax: 0 },
        { min: 317500, max: 322499, tax: 0 },
        { min: 322500, max: 327499, tax: 0 },
        { min: 327500, max: 332499, tax: 0 },
        { min: 332500, max: 337499, tax: 0 },
        { min: 337500, max: 342499, tax: 0 },
        { min: 342500, max: 347499, tax: 0 },
        { min: 347500, max: 352499, tax: 0 },
        { min: 352500, max: 357499, tax: 0 },
        { min: 357500, max: 362499, tax: 0 },
        { min: 362500, max: 367499, tax: 0 },
        { min: 367500, max: 372499, tax: 0 },
        { min: 372500, max: 377499, tax: 0 },
        { min: 377500, max: 382499, tax: 0 },
        { min: 382500, max: 387499, tax: 0 },
        { min: 387500, max: 392499, tax: 0 },
        { min: 392500, max: 397499, tax: 0 },
        { min: 397500, max: 402499, tax: 0 },
        { min: 402500, max: 407499, tax: 0 },
        { min: 407500, max: 412499, tax: 0 },
        { min: 412500, max: 417499, tax: 0 },
        { min: 417500, max: 422499, tax: 0 },
        { min: 422500, max: 427499, tax: 0 },
        { min: 427500, max: 432499, tax: 0 },
        { min: 432500, max: 437499, tax: 0 },
        { min: 437500, max: 442499, tax: 0 },
        { min: 442500, max: 447499, tax: 0 },
        { min: 447500, max: 452499, tax: 0 },
        { min: 452500, max: 457499, tax: 0 },
        { min: 457500, max: 462499, tax: 0 },
        { min: 462500, max: 467499, tax: 0 },
        { min: 467500, max: 472499, tax: 0 },
        { min: 472500, max: 477499, tax: 0 },
        { min: 477500, max: 482499, tax: 0 },
        { min: 482500, max: 487499, tax: 0 },
        { min: 487500, max: 492499, tax: 0 },
        { min: 492500, max: 497499, tax: 51 },
        { min: 497500, max: 502499, tax: 180 },
        { min: 502500, max: 507499, tax: 309 },
        { min: 507500, max: 512499, tax: 439 },
        { min: 512500, max: 517499, tax: 568 },
        { min: 517500, max: 522499, tax: 745 },
        { min: 522500, max: 527499, tax: 961 },
        { min: 527500, max: 532499, tax: 1176 },
        { min: 532500, max: 537499, tax: 1392 },
        { min: 537500, max: 542499, tax: 1607 },
        { min: 542500, max: 547499, tax: 1979 },
        { min: 547500, max: 552499, tax: 2410 },
        { min: 552500, max: 557499, tax: 2841 },
        { min: 557500, max: 562499, tax: 3272 },
        { min: 562500, max: 567499, tax: 3703 },
        { min: 567500, max: 572499, tax: 4134 },
        { min: 572500, max: 577499, tax: 4565 },
        { min: 577500, max: 582499, tax: 4996 },
        { min: 582500, max: 587499, tax: 5427 },
        { min: 587500, max: 592499, tax: 5870 },
        { min: 592500, max: 597499, tax: 6517 },
        { min: 597500, max: 602499, tax: 7163 },
        { min: 602500, max: 607499, tax: 7810 },
        { min: 607500, max: 612499, tax: 8456 },
        { min: 612500, max: 617499, tax: 9103 },
        { min: 617500, max: 622499, tax: 9749 },
        { min: 622500, max: 627499, tax: 10396 },
        { min: 627500, max: 632499, tax: 11042 },
        { min: 632500, max: 637499, tax: 11689 },
        { min: 637500, max: 642499, tax: 12335 },
        { min: 642500, max: 647499, tax: 12982 },
        { min: 647500, max: 652499, tax: 13628 },
        { min: 652500, max: 657499, tax: 14275 },
        { min: 657500, max: 662499, tax: 14921 },
        { min: 662500, max: 667499, tax: 15568 },
        { min: 667500, max: 672499, tax: 16214 },
        { min: 672500, max: 677499, tax: 16861 },
        { min: 677500, max: 682499, tax: 17507 },
        { min: 682500, max: 687499, tax: 18154 },
        { min: 687500, max: 692499, tax: 18800 },
        { min: 692500, max: 697499, tax: 19447 },
        { min: 697500, max: 702499, tax: 20093 },
        { min: 702500, max: 707499, tax: 20740 },
        { min: 707500, max: 712499, tax: 21386 },
        { min: 712500, max: 717499, tax: 22033 },
        { min: 717500, max: 722499, tax: 22679 },
        { min: 722500, max: 727499, tax: 23326 },
        { min: 727500, max: 732499, tax: 23972 },
        { min: 732500, max: 737499, tax: 24619 },
        { min: 737500, max: 742499, tax: 25265 },
        { min: 742500, max: 747499, tax: 25912 },
        { min: 747500, max: 752499, tax: 26558 },
        { min: 752500, max: 757499, tax: 27205 },
        { min: 757500, max: 762499, tax: 27851 },
        { min: 762500, max: 767499, tax: 28498 },
        { min: 767500, max: 772499, tax: 29144 },
        { min: 772500, max: 777499, tax: 29791 },
        { min: 777500, max: 782499, tax: 30437 },
        { min: 782500, max: 787499, tax: 31084 },
        { min: 787500, max: 792499, tax: 31730 },
        { min: 792500, max: 797499, tax: 32377 },
        { min: 797500, max: 802499, tax: 33023 },
        { min: 802500, max: 807499, tax: 33670 },
        { min: 807500, max: 812499, tax: 34316 },
        { min: 812500, max: 817499, tax: 34963 },
        { min: 817500, max: 822499, tax: 35609 },
        { min: 822500, max: 827499, tax: 36256 },
        { min: 827500, max: 832499, tax: 36902 },
        { min: 832500, max: 837499, tax: 37549 },
        { min: 837500, max: 842499, tax: 38195 },
        { min: 842500, max: 847499, tax: 38842 },
        { min: 847500, max: 852499, tax: 39488 },
        { min: 852500, max: 857499, tax: 40135 },
        { min: 857500, max: 862499, tax: 40781 },
        { min: 862500, max: 867499, tax: 41428 },
        { min: 867500, max: 872499, tax: 42074 },
        { min: 872500, max: 877499, tax: 42721 },
        { min: 877500, max: 882499, tax: 43367 },
        { min: 882500, max: 887499, tax: 44014 },
        { min: 887500, max: 892499, tax: 44660 },
        { min: 892500, max: 897499, tax: 45307 },
        { min: 897500, max: 902499, tax: 45953 },
        { min: 902500, max: 907499, tax: 46600 },
        { min: 907500, max: 912499, tax: 47246 },
        { min: 912500, max: 917499, tax: 47893 },
        { min: 917500, max: 922499, tax: 48539 },
        { min: 922500, max: 927499, tax: 49186 },
        { min: 927500, max: 932499, tax: 49832 },
        { min: 932500, max: 937499, tax: 50479 },
        { min: 937500, max: 942499, tax: 51125 },
        { min: 942500, max: 947499, tax: 51772 },
        { min: 947500, max: 952499, tax: 52418 },
        { min: 952500, max: 957499, tax: 53065 },
        { min: 957500, max: 962499, tax: 53711 },
        { min: 962500, max: 967499, tax: 54358 },
        { min: 967500, max: 972499, tax: 55004 },
        { min: 972500, max: 977499, tax: 55651 },
        { min: 977500, max: 982499, tax: 56297 },
        { min: 982500, max: 987499, tax: 56944 },
        { min: 987500, max: 992499, tax: 57590 },
        { min: 992500, max: 997499, tax: 58237 },
        { min: 997500, max: 1002499, tax: 58883 },
        { min: 1002500, max: 1007499, tax: 59530 },
        { min: 1007500, max: 1012499, tax: 60176 },
        { min: 1012500, max: 1017499, tax: 60823 },
        { min: 1017500, max: 1022499, tax: 61469 },
        { min: 1022500, max: 1027499, tax: 62116 },
        { min: 1027500, max: 1032499, tax: 62762 },
        { min: 1032500, max: 1037499, tax: 63409 },
        { min: 1037500, max: 1042499, tax: 64055 },
        { min: 1042500, max: 1047499, tax: 64702 },
        { min: 1047500, max: 1052499, tax: 65348 },
        { min: 1052500, max: 1057499, tax: 65995 },
        { min: 1057500, max: 1062499, tax: 66641 },
        { min: 1062500, max: 1067499, tax: 67288 },
        { min: 1067500, max: 1072499, tax: 67934 },
        { min: 1072500, max: 1077499, tax: 68581 },
        { min: 1077500, max: 1082499, tax: 69227 },
        { min: 1082500, max: 1087499, tax: 69874 },
        { min: 1087500, max: 1092499, tax: 70520 },
        { min: 1092500, max: 1097499, tax: 71167 },
        { min: 1097500, max: 1102499, tax: 71813 },
        { min: 1102500, max: 1107499, tax: 72460 },
        { min: 1107500, max: 1112499, tax: 73106 },
        { min: 1112500, max: 1117499, tax: 73753 },
        { min: 1117500, max: 1122499, tax: 74399 },
        { min: 1122500, max: 1127499, tax: 75046 },
        { min: 1127500, max: 1132499, tax: 75692 },
        { min: 1132500, max: 1137499, tax: 76339 },
        { min: 1137500, max: 1142499, tax: 76985 },
        { min: 1142500, max: 1147499, tax: 77632 },
        { min: 1147500, max: 1152499, tax: 78278 },
        { min: 1152500, max: 1157499, tax: 78925 },
        { min: 1157500, max: 1162499, tax: 79571 },
        { min: 1162500, max: 1167499, tax: 80218 },
        { min: 1167500, max: 1172499, tax: 80864 },
        { min: 1172500, max: 1177499, tax: 81511 },
        { min: 1177500, max: 1182499, tax: 82157 },
        { min: 1182500, max: 1187499, tax: 82804 },
        { min: 1187500, max: 1192499, tax: 83450 },
        { min: 1192500, max: 1197499, tax: 84097 },
        { min: 1197500, max: 1202499, tax: 84743 },
        { min: 1202500, max: 1207499, tax: 85390 },
        { min: 1207500, max: 1212499, tax: 86036 },
        { min: 1212500, max: 1217499, tax: 86683 },
        { min: 1217500, max: 1222499, tax: 87329 },
        { min: 1222500, max: 1227499, tax: 87976 },
        { min: 1227500, max: 1232499, tax: 88622 },
        { min: 1232500, max: 1237499, tax: 89269 },
        { min: 1237500, max: 1242499, tax: 89915 },
        { min: 1242500, max: 1247499, tax: 90562 },
        { min: 1247500, max: 1252499, tax: 91208 },
        { min: 1252500, max: 1257499, tax: 91855 },
        { min: 1257500, max: 1262499, tax: 92501 },
        { min: 1262500, max: 1267499, tax: 93148 },
        { min: 1267500, max: 1272499, tax: 93794 },
        { min: 1272500, max: 1277499, tax: 94441 },
        { min: 1277500, max: 1282499, tax: 95087 },
        { min: 1282500, max: 1287499, tax: 95734 },
        { min: 1287500, max: 1292499, tax: 96380 },
        { min: 1292500, max: 1297499, tax: 97027 },
        { min: 1297500, max: 1302499, tax: 97673 },
        { min: 1302500, max: 1307499, tax: 98320 },
        { min: 1307500, max: 1312499, tax: 98966 },
        { min: 1312500, max: 1317499, tax: 99613 },
        { min: 1317500, max: 1322499, tax: 100259 },
        { min: 1322500, max: 1327499, tax: 100906 },
        { min: 1327500, max: 1332499, tax: 101552 },
        { min: 1332500, max: 1337499, tax: 102199 },
        { min: 1337500, max: 1342499, tax: 102845 },
        { min: 1342500, max: 1347499, tax: 103492 },
        { min: 1347500, max: 1352499, tax: 104138 },
        { min: 1352500, max: 1357499, tax: 104785 },
        { min: 1357500, max: 1362499, tax: 105431 },
        { min: 1362500, max: 1367499, tax: 106078 },
        { min: 1367500, max: 1372499, tax: 106724 },
        { min: 1372500, max: 1377499, tax: 107371 },
        { min: 1377500, max: 1382499, tax: 108017 },
        { min: 1382500, max: 1387499, tax: 108664 },
        { min: 1387500, max: 1392499, tax: 109310 },
        { min: 1392500, max: 1397499, tax: 109957 },
        { min: 1397500, max: 1402499, tax: 110603 },
        { min: 1402500, max: 1407499, tax: 111250 },
        { min: 1407500, max: 1412499, tax: 111896 },
        { min: 1412500, max: 1417499, tax: 112543 },
        { min: 1417500, max: 1422499, tax: 113189 },
        { min: 1422500, max: 1427499, tax: 113836 },
        { min: 1427500, max: 1432499, tax: 114482 },
        { min: 1432500, max: 1437499, tax: 115129 },
        { min: 1437500, max: 1442499, tax: 115775 },
        { min: 1442500, max: 1447499, tax: 116422 },
        { min: 1447500, max: 1452499, tax: 117068 },
        { min: 1452500, max: 1457499, tax: 117715 },
        { min: 1457500, max: 1462499, tax: 118361 },
        { min: 1462500, max: 1467499, tax: 119008 },
        { min: 1467500, max: 1472499, tax: 119654 },
        { min: 1472500, max: 1477499, tax: 120301 },
        { min: 1477500, max: 1482499, tax: 120947 },
        { min: 1482500, max: 1487499, tax: 121594 },
        { min: 1487500, max: 1492499, tax: 122240 },
        { min: 1492500, max: 1497499, tax: 122887 },
        { min: 1497500, max: 1502499, tax: 123533 }
    ],
    "5": [ // 5 أطفال
        { min: 597500, max: 602499, tax: 3387 },
        { min: 602500, max: 607499, tax: 3838 },
        { min: 607500, max: 612499, tax: 4249 },
        { min: 612500, max: 617499, tax: 4680 },
        { min: 617500, max: 622499, tax: 5111 },
        { min: 622500, max: 627499, tax: 5542 },
        { min: 627500, max: 632499, tax: 6042 },
        { min: 632500, max: 637499, tax: 6689 },
        { min: 637500, max: 642499, tax: 7335 },
        { min: 642500, max: 647499, tax: 7982 },
        { min: 647500, max: 652499, tax: 8628 },
        { min: 652500, max: 657499, tax: 9275 },
        { min: 657500, max: 662499, tax: 9921 },
        { min: 662500, max: 667499, tax: 10568 },
        { min: 667500, max: 672499, tax: 11214 },
        { min: 672500, max: 677499, tax: 11861 },
        { min: 677500, max: 682499, tax: 12507 },
        { min: 682500, max: 687499, tax: 13154 },
        { min: 687500, max: 692499, tax: 13800 },
        { min: 692500, max: 697499, tax: 14447 },
        { min: 697500, max: 702499, tax: 15093 },
        { min: 702500, max: 707499, tax: 15740 },
        { min: 707500, max: 712499, tax: 16386 },
        { min: 712500, max: 717499, tax: 17033 },
        { min: 717500, max: 722499, tax: 17679 },
        { min: 722500, max: 727499, tax: 18326 },
        { min: 727500, max: 732499, tax: 18972 },
        { min: 732500, max: 737499, tax: 19619 },
        { min: 737500, max: 742499, tax: 20265 },
        { min: 742500, max: 747499, tax: 20912 },
        { min: 747500, max: 752499, tax: 21558 },
        { min: 752500, max: 757499, tax: 22205 },
        { min: 757500, max: 762499, tax: 22851 },
        { min: 762500, max: 767499, tax: 23498 },
        { min: 767500, max: 772499, tax: 24144 },
        { min: 772500, max: 777499, tax: 24791 },
        { min: 777500, max: 782499, tax: 25437 },
        { min: 782500, max: 787499, tax: 26084 },
        { min: 787500, max: 792499, tax: 26730 },
        { min: 792500, max: 797499, tax: 27377 },
        { min: 797500, max: 802499, tax: 28023 },
        { min: 802500, max: 807499, tax: 28670 },
        { min: 807500, max: 812499, tax: 29316 },
        { min: 812500, max: 817499, tax: 29963 },
        { min: 817500, max: 822499, tax: 30609 },
        { min: 822500, max: 827499, tax: 31256 },
        { min: 827500, max: 832499, tax: 31902 },
        { min: 832500, max: 837499, tax: 32549 },
        { min: 837500, max: 842499, tax: 33195 },
        { min: 842500, max: 847499, tax: 33842 },
        { min: 847500, max: 852499, tax: 34488 },
        { min: 852500, max: 857499, tax: 35135 },
        { min: 857500, max: 862499, tax: 35781 },
        { min: 862500, max: 867499, tax: 36428 },
        { min: 867500, max: 872499, tax: 37074 },
        { min: 872500, max: 877499, tax: 37721 },
        { min: 877500, max: 882499, tax: 38367 },
        { min: 882500, max: 887499, tax: 39014 },
        { min: 887500, max: 892499, tax: 39660 },
        { min: 892500, max: 897499, tax: 40307 },
        { min: 897500, max: 902499, tax: 40953 },
        { min: 902500, max: 907499, tax: 41600 },
        { min: 907500, max: 912499, tax: 42246 },
        { min: 912500, max: 917499, tax: 42893 },
        { min: 917500, max: 922499, tax: 43539 },
        { min: 922500, max: 927499, tax: 44186 },
        { min: 927500, max: 932499, tax: 44832 },
        { min: 932500, max: 937499, tax: 45479 },
        { min: 937500, max: 942499, tax: 46125 },
        { min: 942500, max: 947499, tax: 46772 },
        { min: 947500, max: 952499, tax: 47418 },
        { min: 952500, max: 957499, tax: 48065 },
        { min: 957500, max: 962499, tax: 48711 },
        { min: 962500, max: 967499, tax: 49358 },
        { min: 967500, max: 972499, tax: 50004 },
        { min: 972500, max: 977499, tax: 50651 },
        { min: 977500, max: 982499, tax: 51297 },
        { min: 982500, max: 987499, tax: 51944 },
        { min: 987500, max: 992499, tax: 52590 },
        { min: 992500, max: 997499, tax: 53237 },
        { min: 997500, max: 1002499, tax: 53883 },
        { min: 1002500, max: 1007499, tax: 54530 },
        { min: 1007500, max: 1012499, tax: 55176 },
        { min: 1012500, max: 1017499, tax: 55823 },
        { min: 1017500, max: 1022499, tax: 56469 },
        { min: 1022500, max: 1027499, tax: 57116 },
        { min: 1027500, max: 1032499, tax: 57762 },
        { min: 1032500, max: 1037499, tax: 58409 },
        { min: 1037500, max: 1042499, tax: 59055 },
        { min: 1042500, max: 1047499, tax: 59702 },
        { min: 1047500, max: 1052499, tax: 60348 },
        { min: 1052500, max: 1057499, tax: 60995 },
        { min: 1057500, max: 1062499, tax: 61641 },
        { min: 1062500, max: 1067499, tax: 62288 },
        { min: 1067500, max: 1072499, tax: 62934 },
        { min: 1072500, max: 1077499, tax: 63581 },
        { min: 1077500, max: 1082499, tax: 64227 },
        { min: 1082500, max: 1087499, tax: 64874 },
        { min: 1087500, max: 1092499, tax: 65520 },
        { min: 1092500, max: 1097499, tax: 66167 },
        { min: 1097500, max: 1102499, tax: 66813 },
        { min: 1102500, max: 1107499, tax: 67460 },
        { min: 1107500, max: 1112499, tax: 68106 },
        { min: 1112500, max: 1117499, tax: 68753 },
        { min: 1117500, max: 1122499, tax: 69399 },
        { min: 1122500, max: 1127499, tax: 70046 },
        { min: 1127500, max: 1132499, tax: 70692 },
        { min: 1132500, max: 1137499, tax: 71339 },
        { min: 1137500, max: 1142499, tax: 71985 },
        { min: 1142500, max: 1147499, tax: 72632 },
        { min: 1147500, max: 1152499, tax: 73278 },
        { min: 1152500, max: 1157499, tax: 73925 },
        { min: 1157500, max: 1162499, tax: 74571 },
        { min: 1162500, max: 1167499, tax: 75218 },
        { min: 1167500, max: 1172499, tax: 75864 },
        { min: 1172500, max: 1177499, tax: 76511 },
        { min: 1177500, max: 1182499, tax: 77157 },
        { min: 1182500, max: 1187499, tax: 77804 },
        { min: 1187500, max: 1192499, tax: 78450 },
        { min: 1192500, max: 1197499, tax: 79097 },
        { min: 1197500, max: 1202499, tax: 79743 },
        { min: 1202500, max: 1207499, tax: 80390 },
        { min: 1207500, max: 1212499, tax: 81036 },
        { min: 1212500, max: 1217499, tax: 81683 },
        { min: 1217500, max: 1222499, tax: 82329 },
        { min: 1222500, max: 1227499, tax: 82976 },
        { min: 1227500, max: 1232499, tax: 83622 },
        { min: 1232500, max: 1237499, tax: 84269 },
        { min: 1237500, max: 1242499, tax: 84915 },
        { min: 1242500, max: 1247499, tax: 85562 },
        { min: 1247500, max: 1252499, tax: 86208 },
        { min: 1252500, max: 1257499, tax: 86855 },
        { min: 1257500, max: 1262499, tax: 87501 },
        { min: 1262500, max: 1267499, tax: 88148 },
        { min: 1267500, max: 1272499, tax: 88794 },
        { min: 1272500, max: 1277499, tax: 89441 },
        { min: 1277500, max: 1282499, tax: 90087 },
        { min: 1282500, max: 1287499, tax: 90734 },
        { min: 1287500, max: 1292499, tax: 91380 },
        { min: 1292500, max: 1297499, tax: 92027 },
        { min: 1297500, max: 1302499, tax: 92673 },
        { min: 1302500, max: 1307499, tax: 93320 },
        { min: 1307500, max: 1312499, tax: 93966 },
        { min: 1312500, max: 1317499, tax: 94613 },
        { min: 1317500, max: 1322499, tax: 95259 },
        { min: 1322500, max: 1327499, tax: 95906 },
        { min: 1327500, max: 1332499, tax: 96552 },
        { min: 1332500, max: 1337499, tax: 97199 },
        { min: 1337500, max: 1342499, tax: 97845 },
        { min: 1342500, max: 1347499, tax: 98492 },
        { min: 1347500, max: 1352499, tax: 99138 },
        { min: 1352500, max: 1357499, tax: 99785 },
        { min: 1357500, max: 1362499, tax: 100431 },
        { min: 1362500, max: 1367499, tax: 101078 },
        { min: 1367500, max: 1372499, tax: 101724 },
        { min: 1372500, max: 1377499, tax: 102371 },
        { min: 1377500, max: 1382499, tax: 103017 },
        { min: 1382500, max: 1387499, tax: 103664 },
        { min: 1387500, max: 1392499, tax: 104310 },
        { min: 1392500, max: 1397499, tax: 104957 },
        { min: 1397500, max: 1402499, tax: 105603 },
        { min: 1402500, max: 1407499, tax: 106250 },
        { min: 1407500, max: 1412499, tax: 106896 },
        { min: 1412500, max: 1417499, tax: 107543 },
        { min: 1417500, max: 1422499, tax: 108189 },
        { min: 1422500, max: 1427499, tax: 108836 },
        { min: 1427500, max: 1432499, tax: 109482 },
        { min: 1432500, max: 1437499, tax: 110129 },
        { min: 1437500, max: 1442499, tax: 110775 },
        { min: 1442500, max: 1447499, tax: 111422 },
        { min: 1447500, max: 1452499, tax: 112068 },
        { min: 1452500, max: 1457499, tax: 112715 },
        { min: 1457500, max: 1462499, tax: 113361 },
        { min: 1462500, max: 1467499, tax: 114008 },
        { min: 1467500, max: 1472499, tax: 114654 },
        { min: 1472500, max: 1477499, tax: 115301 },
        { min: 1477500, max: 1482499, tax: 115947 },
        { min: 1482500, max: 1487499, tax: 116594 },
        { min: 1487500, max: 1492499, tax: 117240 },
        { min: 1492500, max: 1497499, tax: 117887 },
        { min: 1497500, max: 1502499, tax: 118533 }
    ],
    "6": [ // 6 أطفال
        { min: 597500, max: 602499, tax: 1887 },
        { min: 602500, max: 607499, tax: 2338 },
        { min: 607500, max: 612499, tax: 2749 },
        { min: 612500, max: 617499, tax: 3180 },
        { min: 617500, max: 622499, tax: 3611 },
        { min: 622500, max: 627499, tax: 4042 },
        { min: 627500, max: 632499, tax: 4542 },
        { min: 632500, max: 637499, tax: 5189 },
        { min: 637500, max: 642499, tax: 5835 },
        { min: 642500, max: 647499, tax: 6482 },
        { min: 647500, max: 652499, tax: 7128 },
        { min: 652500, max: 657499, tax: 7775 },
        { min: 657500, max: 662499, tax: 8421 },
        { min: 662500, max: 667499, tax: 9068 },
        { min: 667500, max: 672499, tax: 9714 },
        { min: 672500, max: 677499, tax: 10361 },
        { min: 677500, max: 682499, tax: 11007 },
        { min: 682500, max: 687499, tax: 11654 },
        { min: 687500, max: 692499, tax: 12300 },
        { min: 692500, max: 697499, tax: 12947 },
        { min: 697500, max: 702499, tax: 13593 },
        { min: 702500, max: 707499, tax: 14240 },
        { min: 707500, max: 712499, tax: 14886 },
        { min: 712500, max: 717499, tax: 15533 },
        { min: 717500, max: 722499, tax: 16179 },
        { min: 722500, max: 727499, tax: 16826 },
        { min: 727500, max: 732499, tax: 17472 },
        { min: 732500, max: 737499, tax: 18119 },
        { min: 737500, max: 742499, tax: 18765 },
        { min: 742500, max: 747499, tax: 19412 },
        { min: 747500, max: 752499, tax: 20058 },
        { min: 752500, max: 757499, tax: 20705 },
        { min: 757500, max: 762499, tax: 21351 },
        { min: 762500, max: 767499, tax: 21998 },
        { min: 767500, max: 772499, tax: 22644 },
        { min: 772500, max: 777499, tax: 23291 },
        { min: 777500, max: 782499, tax: 23937 },
        { min: 782500, max: 787499, tax: 24584 },
        { min: 787500, max: 792499, tax: 25230 },
        { min: 792500, max: 797499, tax: 25877 },
        { min: 797500, max: 802499, tax: 26523 },
        { min: 802500, max: 807499, tax: 27170 },
        { min: 807500, max: 812499, tax: 27816 },
        { min: 812500, max: 817499, tax: 28463 },
        { min: 817500, max: 822499, tax: 29109 },
        { min: 822500, max: 827499, tax: 29756 },
        { min: 827500, max: 832499, tax: 30402 },
        { min: 832500, max: 837499, tax: 31049 },
        { min: 837500, max: 842499, tax: 31695 },
        { min: 842500, max: 847499, tax: 32342 },
        { min: 847500, max: 852499, tax: 32988 },
        { min: 852500, max: 857499, tax: 33635 },
        { min: 857500, max: 862499, tax: 34281 },
        { min: 862500, max: 867499, tax: 34928 },
        { min: 867500, max: 872499, tax: 35574 },
        { min: 872500, max: 877499, tax: 36221 },
        { min: 877500, max: 882499, tax: 36867 },
        { min: 882500, max: 887499, tax: 37514 },
        { min: 887500, max: 892499, tax: 38160 },
        { min: 892500, max: 897499, tax: 38807 },
        { min: 897500, max: 902499, tax: 39453 },
        { min: 902500, max: 907499, tax: 40100 },
        { min: 907500, max: 912499, tax: 40746 },
        { min: 912500, max: 917499, tax: 41393 },
        { min: 917500, max: 922499, tax: 42039 },
        { min: 922500, max: 927499, tax: 42686 },
        { min: 927500, max: 932499, tax: 43332 },
        { min: 932500, max: 937499, tax: 43979 },
        { min: 937500, max: 942499, tax: 44625 },
        { min: 942500, max: 947499, tax: 45272 },
        { min: 947500, max: 952499, tax: 45918 },
        { min: 952500, max: 957499, tax: 46565 },
        { min: 957500, max: 962499, tax: 47211 },
        { min: 962500, max: 967499, tax: 47858 },
        { min: 967500, max: 972499, tax: 48504 },
        { min: 972500, max: 977499, tax: 49151 },
        { min: 977500, max: 982499, tax: 49797 },
        { min: 982500, max: 987499, tax: 50444 },
        { min: 987500, max: 992499, tax: 51090 },
        { min: 992500, max: 997499, tax: 51737 },
        { min: 997500, max: 1002499, tax: 52383 },
        { min: 1002500, max: 1007499, tax: 53030 },
        { min: 1007500, max: 1012499, tax: 53676 },
        { min: 1012500, max: 1017499, tax: 54323 },
        { min: 1017500, max: 1022499, tax: 54969 },
        { min: 1022500, max: 1027499, tax: 55616 },
        { min: 1027500, max: 1032499, tax: 56262 },
        { min: 1032500, max: 1037499, tax: 56909 },
        { min: 1037500, max: 1042499, tax: 57555 },
        { min: 1042500, max: 1047499, tax: 58202 },
        { min: 1047500, max: 1052499, tax: 58848 },
        { min: 1052500, max: 1057499, tax: 59495 },
        { min: 1057500, max: 1062499, tax: 60141 },
        { min: 1062500, max: 1067499, tax: 60788 },
        { min: 1067500, max: 1072499, tax: 61434 },
        { min: 1072500, max: 1077499, tax: 62081 },
        { min: 1077500, max: 1082499, tax: 62727 },
        { min: 1082500, max: 1087499, tax: 63374 },
        { min: 1087500, max: 1092499, tax: 64020 },
        { min: 1092500, max: 1097499, tax: 64667 },
        { min: 1097500, max: 1102499, tax: 65313 },
        { min: 1102500, max: 1107499, tax: 65960 },
        { min: 1107500, max: 1112499, tax: 66606 },
        { min: 1112500, max: 1117499, tax: 67253 },
        { min: 1117500, max: 1122499, tax: 67899 },
        { min: 1122500, max: 1127499, tax: 68546 },
        { min: 1127500, max: 1132499, tax: 69192 },
        { min: 1132500, max: 1137499, tax: 69839 },
        { min: 1137500, max: 1142499, tax: 70485 },
        { min: 1142500, max: 1147499, tax: 71132 },
        { min: 1147500, max: 1152499, tax: 71778 },
        { min: 1152500, max: 1157499, tax: 72425 },
        { min: 1157500, max: 1162499, tax: 73071 },
        { min: 1162500, max: 1167499, tax: 73718 },
        { min: 1167500, max: 1172499, tax: 74364 },
        { min: 1172500, max: 1177499, tax: 75011 },
        { min: 1177500, max: 1182499, tax: 75657 },
        { min: 1182500, max: 1187499, tax: 76304 },
        { min: 1187500, max: 1192499, tax: 76950 },
        { min: 1192500, max: 1197499, tax: 77597 },
        { min: 1197500, max: 1202499, tax: 78243 },
        { min: 1202500, max: 1207499, tax: 78890 },
        { min: 1207500, max: 1212499, tax: 79536 },
        { min: 1212500, max: 1217499, tax: 80183 },
        { min: 1217500, max: 1222499, tax: 80829 },
        { min: 1222500, max: 1227499, tax: 81476 },
        { min: 1227500, max: 1232499, tax: 82122 },
        { min: 1232500, max: 1237499, tax: 82769 },
        { min: 1237500, max: 1242499, tax: 83415 },
        { min: 1242500, max: 1247499, tax: 84062 },
        { min: 1247500, max: 1252499, tax: 84708 },
        { min: 1252500, max: 1257499, tax: 85355 },
        { min: 1257500, max: 1262499, tax: 86001 },
        { min: 1262500, max: 1267499, tax: 86648 },
        { min: 1267500, max: 1272499, tax: 87294 },
        { min: 1272500, max: 1277499, tax: 87941 },
        { min: 1277500, max: 1282499, tax: 88587 },
        { min: 1282500, max: 1287499, tax: 89234 },
        { min: 1287500, max: 1292499, tax: 89880 },
        { min: 1292500, max: 1297499, tax: 90527 },
        { min: 1297500, max: 1302499, tax: 91173 },
        { min: 1302500, max: 1307499, tax: 91820 },
        { min: 1307500, max: 1312499, tax: 92466 },
        { min: 1312500, max: 1317499, tax: 93113 },
        { min: 1317500, max: 1322499, tax: 93759 },
        { min: 1322500, max: 1327499, tax: 94406 },
        { min: 1327500, max: 1332499, tax: 95052 },
        { min: 1332500, max: 1337499, tax: 95699 },
        { min: 1337500, max: 1342499, tax: 96345 },
        { min: 1342500, max: 1347499, tax: 96992 },
        { min: 1347500, max: 1352499, tax: 97638 },
        { min: 1352500, max: 1357499, tax: 98285 },
        { min: 1357500, max: 1362499, tax: 98931 },
        { min: 1362500, max: 1367499, tax: 99578 },
        { min: 1367500, max: 1372499, tax: 100224 },
        { min: 1372500, max: 1377499, tax: 100871 },
        { min: 1377500, max: 1382499, tax: 101517 },
        { min: 1382500, max: 1387499, tax: 102164 },
        { min: 1387500, max: 1392499, tax: 102810 },
        { min: 1392500, max: 1397499, tax: 103457 },
        { min: 1397500, max: 1402499, tax: 104103 },
        { min: 1402500, max: 1407499, tax: 104750 },
        { min: 1407500, max: 1412499, tax: 105396 },
        { min: 1412500, max: 1417499, tax: 106043 },
        { min: 1417500, max: 1422499, tax: 106689 },
        { min: 1422500, max: 1427499, tax: 107336 },
        { min: 1427500, max: 1432499, tax: 107982 },
        { min: 1432500, max: 1437499, tax: 108629 },
        { min: 1437500, max: 1442499, tax: 109275 },
        { min: 1442500, max: 1447499, tax: 109922 },
        { min: 1447500, max: 1452499, tax: 110568 },
        { min: 1452500, max: 1457499, tax: 111215 },
        { min: 1457500, max: 1462499, tax: 111861 },
        { min: 1462500, max: 1467499, tax: 112508 },
        { min: 1467500, max: 1472499, tax: 113154 },
        { min: 1472500, max: 1477499, tax: 113801 },
        { min: 1477500, max: 1482499, tax: 114447 },
        { min: 1482500, max: 1487499, tax: 115094 },
        { min: 1487500, max: 1492499, tax: 115740 },
        { min: 1492500, max: 1497499, tax: 116387 },
        { min: 1497500, max: 1502499, tax: 117033 }
    ],
    "7": [ // 7 أطفال
        { min: 597500, max: 602499, tax: 387 },
        { min: 602500, max: 607499, tax: 838 },
        { min: 607500, max: 612499, tax: 1249 },
        { min: 612500, max: 617499, tax: 1680 },
        { min: 617500, max: 622499, tax: 2111 },
        { min: 622500, max: 627499, tax: 2542 },
        { min: 627500, max: 632499, tax: 3042 },
        { min: 632500, max: 637499, tax: 3689 },
        { min: 637500, max: 642499, tax: 4335 },
        { min: 642500, max: 647499, tax: 4982 },
        { min: 647500, max: 652499, tax: 5628 },
        { min: 652500, max: 657499, tax: 6275 },
        { min: 657500, max: 662499, tax: 6921 },
        { min: 662500, max: 667499, tax: 7568 },
        { min: 667500, max: 672499, tax: 8214 },
        { min: 672500, max: 677499, tax: 8861 },
        { min: 677500, max: 682499, tax: 9507 },
        { min: 682500, max: 687499, tax: 10154 },
        { min: 687500, max: 692499, tax: 10800 },
        { min: 692500, max: 697499, tax: 11447 },
        { min: 697500, max: 702499, tax: 12093 },
        { min: 702500, max: 707499, tax: 12740 },
        { min: 707500, max: 712499, tax: 13386 },
        { min: 712500, max: 717499, tax: 14033 },
        { min: 717500, max: 722499, tax: 14679 },
        { min: 722500, max: 727499, tax: 15326 },
        { min: 727500, max: 732499, tax: 15972 },
        { min: 732500, max: 737499, tax: 16619 },
        { min: 737500, max: 742499, tax: 17265 },
        { min: 742500, max: 747499, tax: 17912 },
        { min: 747500, max: 752499, tax: 18558 },
        { min: 752500, max: 757499, tax: 19205 },
        { min: 757500, max: 762499, tax: 19851 },
        { min: 762500, max: 767499, tax: 20498 },
        { min: 767500, max: 772499, tax: 21144 },
        { min: 772500, max: 777499, tax: 21791 },
        { min: 777500, max: 782499, tax: 22437 },
        { min: 782500, max: 787499, tax: 23084 },
        { min: 787500, max: 792499, tax: 23730 },
        { min: 792500, max: 797499, tax: 24377 },
        { min: 797500, max: 802499, tax: 25023 },
        { min: 802500, max: 807499, tax: 25670 },
        { min: 807500, max: 812499, tax: 26316 },
        { min: 812500, max: 817499, tax: 26963 },
        { min: 817500, max: 822499, tax: 27609 },
        { min: 822500, max: 827499, tax: 28256 },
        { min: 827500, max: 832499, tax: 28902 },
        { min: 832500, max: 837499, tax: 29549 },
        { min: 837500, max: 842499, tax: 30195 },
        { min: 842500, max: 847499, tax: 30842 },
        { min: 847500, max: 852499, tax: 31488 },
        { min: 852500, max: 857499, tax: 32135 },
        { min: 857500, max: 862499, tax: 32781 },
        { min: 862500, max: 867499, tax: 33428 },
        { min: 867500, max: 872499, tax: 34074 },
        { min: 872500, max: 877499, tax: 34721 },
        { min: 877500, max: 882499, tax: 35367 },
        { min: 882500, max: 887499, tax: 36014 },
        { min: 887500, max: 892499, tax: 36660 },
        { min: 892500, max: 897499, tax: 37307 },
        { min: 897500, max: 902499, tax: 37953 },
        { min: 902500, max: 907499, tax: 38600 },
        { min: 907500, max: 912499, tax: 39246 },
        { min: 912500, max: 917499, tax: 39893 },
        { min: 917500, max: 922499, tax: 40539 },
        { min: 922500, max: 927499, tax: 41186 },
        { min: 927500, max: 932499, tax: 41832 },
        { min: 932500, max: 937499, tax: 42479 },
        { min: 937500, max: 942499, tax: 43125 },
        { min: 942500, max: 947499, tax: 43772 },
        { min: 947500, max: 952499, tax: 44418 },
        { min: 952500, max: 957499, tax: 45065 },
        { min: 957500, max: 962499, tax: 45711 },
        { min: 962500, max: 967499, tax: 46358 },
        { min: 967500, max: 972499, tax: 47004 },
        { min: 972500, max: 977499, tax: 47651 },
        { min: 977500, max: 982499, tax: 48297 },
        { min: 982500, max: 987499, tax: 48944 },
        { min: 987500, max: 992499, tax: 49590 },
        { min: 992500, max: 997499, tax: 50237 },
        { min: 997500, max: 1002499, tax: 50883 },
        { min: 1002500, max: 1007499, tax: 51530 },
        { min: 1007500, max: 1012499, tax: 52176 },
        { min: 1012500, max: 1017499, tax: 52823 },
        { min: 1017500, max: 1022499, tax: 53469 },
        { min: 1022500, max: 1027499, tax: 54116 },
        { min: 1027500, max: 1032499, tax: 54762 },
        { min: 1032500, max: 1037499, tax: 55409 },
        { min: 1037500, max: 1042499, tax: 56055 },
        { min: 1042500, max: 1047499, tax: 56702 },
        { min: 1047500, max: 1052499, tax: 57348 },
        { min: 1052500, max: 1057499, tax: 57995 },
        { min: 1057500, max: 1062499, tax: 58641 },
        { min: 1062500, max: 1067499, tax: 59288 },
        { min: 1067500, max: 1072499, tax: 59934 },
        { min: 1072500, max: 1077499, tax: 60581 },
        { min: 1077500, max: 1082499, tax: 61227 },
        { min: 1082500, max: 1087499, tax: 61874 },
        { min: 1087500, max: 1092499, tax: 62520 },
        { min: 1092500, max: 1097499, tax: 63167 },
        { min: 1097500, max: 1102499, tax: 63813 },
        { min: 1102500, max: 1107499, tax: 64460 },
        { min: 1107500, max: 1112499, tax: 65106 },
        { min: 1112500, max: 1117499, tax: 65753 },
        { min: 1117500, max: 1122499, tax: 66399 },
        { min: 1122500, max: 1127499, tax: 67046 },
        { min: 1127500, max: 1132499, tax: 67692 },
        { min: 1132500, max: 1137499, tax: 68339 },
        { min: 1137500, max: 1142499, tax: 68985 },
        { min: 1142500, max: 1147499, tax: 69632 },
        { min: 1147500, max: 1152499, tax: 70278 },
        { min: 1152500, max: 1157499, tax: 70925 },
        { min: 1157500, max: 1162499, tax: 71571 },
        { min: 1162500, max: 1167499, tax: 72218 },
        { min: 1167500, max: 1172499, tax: 72864 },
        { min: 1172500, max: 1177499, tax: 73511 },
        { min: 1177500, max: 1182499, tax: 74157 },
        { min: 1182500, max: 1187499, tax: 74804 },
        { min: 1187500, max: 1192499, tax: 75450 },
        { min: 1192500, max: 1197499, tax: 76097 },
        { min: 1197500, max: 1202499, tax: 76743 },
        { min: 1202500, max: 1207499, tax: 77390 },
        { min: 1207500, max: 1212499, tax: 78036 },
        { min: 1212500, max: 1217499, tax: 78683 },
        { min: 1217500, max: 1222499, tax: 79329 },
        { min: 1222500, max: 1227499, tax: 79976 },
        { min: 1227500, max: 1232499, tax: 80622 },
        { min: 1232500, max: 1237499, tax: 81269 },
        { min: 1237500, max: 1242499, tax: 81915 },
        { min: 1242500, max: 1247499, tax: 82562 },
        { min: 1247500, max: 1252499, tax: 83208 },
        { min: 1252500, max: 1257499, tax: 83855 },
        { min: 1257500, max: 1262499, tax: 84501 },
        { min: 1262500, max: 1267499, tax: 85148 },
        { min: 1267500, max: 1272499, tax: 85794 },
        { min: 1272500, max: 1277499, tax: 86441 },
        { min: 1277500, max: 1282499, tax: 87087 },
        { min: 1282500, max: 1287499, tax: 87734 },
        { min: 1287500, max: 1292499, tax: 88380 },
        { min: 1292500, max: 1297499, tax: 89027 },
        { min: 1297500, max: 1302499, tax: 89673 },
        { min: 1302500, max: 1307499, tax: 90320 },
        { min: 1307500, max: 1312499, tax: 90966 },
        { min: 1312500, max: 1317499, tax: 91613 },
        { min: 1317500, max: 1322499, tax: 92259 },
        { min: 1322500, max: 1327499, tax: 92906 },
        { min: 1327500, max: 1332499, tax: 93552 },
        { min: 1332500, max: 1337499, tax: 94199 },
        { min: 1337500, max: 1342499, tax: 94845 },
        { min: 1342500, max: 1347499, tax: 95492 },
        { min: 1347500, max: 1352499, tax: 96138 },
        { min: 1352500, max: 1357499, tax: 96785 },
        { min: 1357500, max: 1362499, tax: 97431 },
        { min: 1362500, max: 1367499, tax: 98078 },
        { min: 1367500, max: 1372499, tax: 98724 },
        { min: 1372500, max: 1377499, tax: 99371 },
        { min: 1377500, max: 1382499, tax: 100017 },
        { min: 1382500, max: 1387499, tax: 100664 },
        { min: 1387500, max: 1392499, tax: 101310 },
        { min: 1392500, max: 1397499, tax: 101957 },
        { min: 1397500, max: 1402499, tax: 102603 },
        { min: 1402500, max: 1407499, tax: 103250 },
        { min: 1407500, max: 1412499, tax: 103896 },
        { min: 1412500, max: 1417499, tax: 104543 },
        { min: 1417500, max: 1422499, tax: 105189 },
        { min: 1422500, max: 1427499, tax: 105836 },
        { min: 1427500, max: 1432499, tax: 106482 },
        { min: 1432500, max: 1437499, tax: 107129 },
        { min: 1437500, max: 1442499, tax: 107775 },
        { min: 1442500, max: 1447499, tax: 108422 },
        { min: 1447500, max: 1452499, tax: 109068 },
        { min: 1452500, max: 1457499, tax: 109715 },
        { min: 1457500, max: 1462499, tax: 110361 },
        { min: 1462500, max: 1467499, tax: 111008 },
        { min: 1467500, max: 1472499, tax: 111654 },
        { min: 1472500, max: 1477499, tax: 112301 },
        { min: 1477500, max: 1482499, tax: 112947 },
        { min: 1482500, max: 1487499, tax: 113594 },
        { min: 1487500, max: 1492499, tax: 114240 },
        { min: 1492500, max: 1497499, tax: 114887 },
        { min: 1497500, max: 1502499, tax: 115533 }
    ],
    "8": [ // 8 أطفال
        { min: 242500, max: 247499, tax: 0 },
        { min: 247500, max: 252499, tax: 0 },
        { min: 252500, max: 257499, tax: 0 },
        { min: 257500, max: 262499, tax: 0 },
        { min: 262500, max: 267499, tax: 0 },
        { min: 267500, max: 272499, tax: 0 },
        { min: 272500, max: 277499, tax: 0 },
        { min: 277500, max: 282499, tax: 0 },
        { min: 282500, max: 287499, tax: 0 },
        { min: 287500, max: 292499, tax: 0 },
        { min: 292500, max: 297499, tax: 0 },
        { min: 297500, max: 302499, tax: 0 },
        { min: 302500, max: 307499, tax: 0 },
        { min: 307500, max: 312499, tax: 0 },
        { min: 312500, max: 317499, tax: 0 },
        { min: 317500, max: 322499, tax: 0 },
        { min: 322500, max: 327499, tax: 0 },
        { min: 327500, max: 332499, tax: 0 },
        { min: 332500, max: 337499, tax: 0 },
        { min: 337500, max: 342499, tax: 0 },
        { min: 342500, max: 347499, tax: 0 },
        { min: 347500, max: 352499, tax: 0 },
        { min: 352500, max: 357499, tax: 0 },
        { min: 357500, max: 362499, tax: 0 },
        { min: 362500, max: 367499, tax: 0 },
        { min: 367500, max: 372499, tax: 0 },
        { min: 372500, max: 377499, tax: 0 },
        { min: 377500, max: 382499, tax: 0 },
        { min: 382500, max: 387499, tax: 0 },
        { min: 387500, max: 392499, tax: 0 },
        { min: 392500, max: 397499, tax: 0 },
        { min: 397500, max: 402499, tax: 0 },
        { min: 402500, max: 407499, tax: 0 },
        { min: 407500, max: 412499, tax: 0 },
        { min: 412500, max: 417499, tax: 0 },
        { min: 417500, max: 422499, tax: 0 },
        { min: 422500, max: 427499, tax: 0 },
        { min: 427500, max: 432499, tax: 0 },
        { min: 432500, max: 437499, tax: 0 },
        { min: 437500, max: 442499, tax: 0 },
        { min: 442500, max: 447499, tax: 0 },
        { min: 447500, max: 452499, tax: 0 },
        { min: 452500, max: 457499, tax: 0 },
        { min: 457500, max: 462499, tax: 0 },
        { min: 462500, max: 467499, tax: 0 },
        { min: 467500, max: 472499, tax: 0 },
        { min: 472500, max: 477499, tax: 0 },
        { min: 477500, max: 482499, tax: 0 },
        { min: 482500, max: 487499, tax: 0 },
        { min: 487500, max: 492499, tax: 0 },
        { min: 492500, max: 497499, tax: 0 },
        { min: 497500, max: 502499, tax: 0 },
        { min: 502500, max: 507499, tax: 0 },
        { min: 507500, max: 512499, tax: 0 },
        { min: 512500, max: 517499, tax: 0 },
        { min: 517500, max: 522499, tax: 0 },
        { min: 522500, max: 527499, tax: 0 },
        { min: 527500, max: 532499, tax: 0 },
        { min: 532500, max: 537499, tax: 0 },
        { min: 537500, max: 542499, tax: 0 },
        { min: 542500, max: 547499, tax: 0 },
        { min: 547500, max: 552499, tax: 0 },
        { min: 552500, max: 557499, tax: 0 },
        { min: 557500, max: 562499, tax: 0 },
        { min: 562500, max: 567499, tax: 0 },
        { min: 567500, max: 572499, tax: 0 },
        { min: 572500, max: 577499, tax: 0 },
        { min: 577500, max: 582499, tax: 0 },
        { min: 582500, max: 587499, tax: 0 },
        { min: 587500, max: 592499, tax: 0 },
        { min: 592500, max: 597499, tax: 0 },
        { min: 597500, max: 602499, tax: 266 },
        { min: 602500, max: 607499, tax: 697 },
        { min: 607500, max: 612499, tax: 1128 },
        { min: 612500, max: 617499, tax: 1559 },
        { min: 617500, max: 622499, tax: 1990 },
        { min: 622500, max: 627499, tax: 2421 },
        { min: 627500, max: 632499, tax: 2921 },
        { min: 632500, max: 637499, tax: 3568 },
        { min: 637500, max: 642499, tax: 4214 },
        { min: 642500, max: 647499, tax: 4861 },
        { min: 647500, max: 652499, tax: 5507 },
        { min: 652500, max: 657499, tax: 6154 },
        { min: 657500, max: 662499, tax: 6800 },
        { min: 662500, max: 667499, tax: 7447 },
        { min: 667500, max: 672499, tax: 8093 },
        { min: 672500, max: 677499, tax: 8740 },
        { min: 677500, max: 682499, tax: 9386 },
        { min: 682500, max: 687499, tax: 10033 },
        { min: 687500, max: 692499, tax: 10679 },
        { min: 692500, max: 697499, tax: 11326 },
        { min: 697500, max: 702499, tax: 11972 },
        { min: 702500, max: 707499, tax: 12619 },
        { min: 707500, max: 712499, tax: 13265 },
        { min: 712500, max: 717499, tax: 13912 },
        { min: 717500, max: 722499, tax: 14558 },
        { min: 722500, max: 727499, tax: 15205 },
        { min: 727500, max: 732499, tax: 15851 },
        { min: 732500, max: 737499, tax: 16498 },
        { min: 737500, max: 742499, tax: 17144 },
        { min: 742500, max: 747499, tax: 17791 },
        { min: 747500, max: 752499, tax: 18437 },
        { min: 752500, max: 757499, tax: 19084 },
        { min: 757500, max: 762499, tax: 19730 },
        { min: 762500, max: 767499, tax: 20377 },
        { min: 767500, max: 772499, tax: 21023 },
        { min: 772500, max: 777499, tax: 21670 },
        { min: 777500, max: 782499, tax: 22316 },
        { min: 782500, max: 787499, tax: 22963 },
        { min: 787500, max: 792499, tax: 23609 },
        { min: 792500, max: 797499, tax: 24256 },
        { min: 797500, max: 802499, tax: 24902 },
        { min: 802500, max: 807499, tax: 25549 },
        { min: 807500, max: 812499, tax: 26195 },
        { min: 812500, max: 817499, tax: 26842 },
        { min: 817500, max: 822499, tax: 27488 },
        { min: 822500, max: 827499, tax: 28135 },
        { min: 827500, max: 832499, tax: 28781 },
        { min: 832500, max: 837499, tax: 29428 },
        { min: 837500, max: 842499, tax: 30074 },
        { min: 842500, max: 847499, tax: 30721 },
        { min: 847500, max: 852499, tax: 31367 },
        { min: 852500, max: 857499, tax: 32014 },
        { min: 857500, max: 862499, tax: 32660 },
        { min: 862500, max: 867499, tax: 33307 },
        { min: 867500, max: 872499, tax: 33953 },
        { min: 872500, max: 877499, tax: 34600 },
        { min: 877500, max: 882499, tax: 35246 },
        { min: 882500, max: 887499, tax: 35893 },
        { min: 887500, max: 892499, tax: 36539 },
        { min: 892500, max: 897499, tax: 37186 },
        { min: 897500, max: 902499, tax: 37832 },
        { min: 902500, max: 907499, tax: 38479 },
        { min: 907500, max: 912499, tax: 39125 },
        { min: 912500, max: 917499, tax: 39772 },
        { min: 917500, max: 922499, tax: 40418 },
        { min: 922500, max: 927499, tax: 41065 },
        { min: 927500, max: 932499, tax: 41711 },
        { min: 932500, max: 937499, tax: 42358 },
        { min: 937500, max: 942499, tax: 43004 },
        { min: 942500, max: 947499, tax: 43651 },
        { min: 947500, max: 952499, tax: 44297 },
        { min: 952500, max: 957499, tax: 44944 },
        { min: 957500, max: 962499, tax: 45590 },
        { min: 962500, max: 967499, tax: 46237 },
        { min: 967500, max: 972499, tax: 46883 },
        { min: 972500, max: 977499, tax: 47530 },
        { min: 977500, max: 982499, tax: 48176 },
        { min: 982500, max: 987499, tax: 48823 },
        { min: 987500, max: 992499, tax: 49469 },
        { min: 992500, max: 997499, tax: 50116 },
        { min: 997500, max: 1002499, tax: 50762 },
        { min: 1002500, max: 1007499, tax: 51409 },
        { min: 1007500, max: 1012499, tax: 52055 },
        { min: 1012500, max: 1017499, tax: 52702 },
        { min: 1017500, max: 1022499, tax: 53348 },
        { min: 1022500, max: 1027499, tax: 53995 },
        { min: 1027500, max: 1032499, tax: 54641 },
        { min: 1032500, max: 1037499, tax: 55288 },
        { min: 1037500, max: 1042499, tax: 55934 },
        { min: 1042500, max: 1047499, tax: 56581 },
        { min: 1047500, max: 1052499, tax: 57227 },
        { min: 1052500, max: 1057499, tax: 57874 },
        { min: 1057500, max: 1062499, tax: 58520 },
        { min: 1062500, max: 1067499, tax: 59167 },
        { min: 1067500, max: 1072499, tax: 59813 },
        { min: 1072500, max: 1077499, tax: 60460 },
        { min: 1077500, max: 1082499, tax: 61106 },
        { min: 1082500, max: 1087499, tax: 61753 },
        { min: 1087500, max: 1092499, tax: 62399 },
        { min: 1092500, max: 1097499, tax: 63046 },
        { min: 1097500, max: 1102499, tax: 63692 },
        { min: 1102500, max: 1107499, tax: 64339 },
        { min: 1107500, max: 1112499, tax: 64985 },
        { min: 1112500, max: 1117499, tax: 65632 },
        { min: 1117500, max: 1122499, tax: 66278 },
        { min: 1122500, max: 1127499, tax: 66925 },
        { min: 1127500, max: 1132499, tax: 67571 },
        { min: 1132500, max: 1137499, tax: 68218 },
        { min: 1137500, max: 1142499, tax: 68864 },
        { min: 1142500, max: 1147499, tax: 69511 },
        { min: 1147500, max: 1152499, tax: 70157 },
        { min: 1152500, max: 1157499, tax: 70804 },
        { min: 1157500, max: 1162499, tax: 71450 },
        { min: 1162500, max: 1167499, tax: 72097 },
        { min: 1167500, max: 1172499, tax: 72743 },
        { min: 1172500, max: 1177499, tax: 73390 },
        { min: 1177500, max: 1182499, tax: 74036 },
        { min: 1182500, max: 1187499, tax: 74683 },
        { min: 1187500, max: 1192499, tax: 75329 },
        { min: 1192500, max: 1197499, tax: 75976 },
        { min: 1197500, max: 1202499, tax: 76622 },
        { min: 1202500, max: 1207499, tax: 77269 },
        { min: 1207500, max: 1212499, tax: 77915 },
        { min: 1212500, max: 1217499, tax: 78562 },
        { min: 1217500, max: 1222499, tax: 79208 },
        { min: 1222500, max: 1227499, tax: 79855 },
        { min: 1227500, max: 1232499, tax: 80501 },
        { min: 1232500, max: 1237499, tax: 81148 },
        { min: 1237500, max: 1242499, tax: 81794 },
        { min: 1242500, max: 1247499, tax: 82441 },
        { min: 1247500, max: 1252499, tax: 83087 },
        { min: 1252500, max: 1257499, tax: 83734 },
        { min: 1257500, max: 1262499, tax: 84380 },
        { min: 1262500, max: 1267499, tax: 85027 },
        { min: 1267500, max: 1272499, tax: 85673 },
        { min: 1272500, max: 1277499, tax: 86320 },
        { min: 1277500, max: 1282499, tax: 86966 },
        { min: 1282500, max: 1287499, tax: 87613 },
        { min: 1287500, max: 1292499, tax: 88259 },
        { min: 1292500, max: 1297499, tax: 88906 },
        { min: 1297500, max: 1302499, tax: 89552 },
        { min: 1302500, max: 1307499, tax: 90199 },
        { min: 1307500, max: 1312499, tax: 90845 },
        { min: 1312500, max: 1317499, tax: 91492 },
        { min: 1317500, max: 1322499, tax: 92138 },
        { min: 1322500, max: 1327499, tax: 92785 },
        { min: 1327500, max: 1332499, tax: 93431 },
        { min: 1332500, max: 1337499, tax: 94078 },
        { min: 1337500, max: 1342499, tax: 94724 },
        { min: 1342500, max: 1347499, tax: 95371 },
        { min: 1347500, max: 1352499, tax: 96017 },
        { min: 1352500, max: 1357499, tax: 96664 },
        { min: 1357500, max: 1362499, tax: 97310 },
        { min: 1362500, max: 1367499, tax: 97957 },
        { min: 1367500, max: 1372499, tax: 98603 },
        { min: 1372500, max: 1377499, tax: 99250 },
        { min: 1377500, max: 1382499, tax: 99896 },
        { min: 1382500, max: 1387499, tax: 100543 },
        { min: 1387500, max: 1392499, tax: 101189 },
        { min: 1392500, max: 1397499, tax: 101836 },
        { min: 1397500, max: 1402499, tax: 102482 },
        { min: 1402500, max: 1407499, tax: 103129 },
        { min: 1407500, max: 1412499, tax: 103775 },
        { min: 1412500, max: 1417499, tax: 104422 },
        { min: 1417500, max: 1422499, tax: 105068 },
        { min: 1422500, max: 1427499, tax: 105715 },
        { min: 1427500, max: 1432499, tax: 106361 },
        { min: 1432500, max: 1437499, tax: 107008 },
        { min: 1437500, max: 1442499, tax: 107654 },
        { min: 1442500, max: 1447499, tax: 108301 },
        { min: 1447500, max: 1452499, tax: 108947 },
        { min: 1452500, max: 1457499, tax: 109594 },
        { min: 1457500, max: 1462499, tax: 110240 },
        { min: 1462500, max: 1467499, tax: 110887 },
        { min: 1467500, max: 1472499, tax: 111533 },
        { min: 1472500, max: 1477499, tax: 112180 },
        { min: 1477500, max: 1482499, tax: 112826 },
        { min: 1482500, max: 1487499, tax: 113473 },
        { min: 1487500, max: 1492499, tax: 114119 },
        { min: 1492500, max: 1497499, tax: 114766 },
        { min: 1497500, max: 1502499, tax: 115412 }
    ]
};

// متغيرات عامة
let currentGrade = '';
let currentStep = '';
let currentChildren = '';

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', function() {
    initializeForm();
    addEventListeners();
    setupTabs();
});

// تهيئة النموذج
function initializeForm() {
    populateGrades();
    hideResults();
}

// إضافة مستمعي الأحداث
function addEventListeners() {
    const gradeSelect = document.getElementById('grade');
    const stepSelect = document.getElementById('step');
    const childrenSelect = document.getElementById('children');

    gradeSelect.addEventListener('change', function() {
        currentGrade = this.value;
        populateSteps();
        validateForm();
    });

    stepSelect.addEventListener('change', function() {
        currentStep = this.value;
        validateForm();
    });

    childrenSelect.addEventListener('change', function() {
        currentChildren = this.value;
        validateForm();
    });
}

// ملء قائمة الدرجات
function populateGrades() {
    const gradeSelect = document.getElementById('grade');
    gradeSelect.innerHTML = '<option value="">-- اختر الدرجة --</option>';

    for (let grade in salariesData) {
        const option = document.createElement('option');
        option.value = grade;
        option.textContent = grade;
        gradeSelect.appendChild(option);
    }
}

// ملء قائمة الاستيبات
function populateSteps() {
    const stepSelect = document.getElementById('step');
    stepSelect.innerHTML = '<option value="">-- اختر الاستيب --</option>';

    if (!currentGrade || !salariesData[currentGrade]) {
        stepSelect.disabled = true;
        return;
    }

    stepSelect.disabled = false;

    for (let step in salariesData[currentGrade]) {
        const option = document.createElement('option');
        option.value = step;
        option.textContent = step;
        stepSelect.appendChild(option);
    }
}

// التحقق من صحة النموذج
function validateForm() {
    const calculateBtn = document.getElementById('calculateBtn');
    const isValid = currentGrade && currentStep && currentChildren !== '';

    calculateBtn.disabled = !isValid;
    calculateBtn.style.opacity = isValid ? '1' : '0.6';
    calculateBtn.style.cursor = isValid ? 'pointer' : 'not-allowed';
}

// إخفاء النتائج
function hideResults() {
    const resultsSection = document.getElementById('results');
    resultsSection.style.display = 'none';
}

// إظهار النتائج
function showResults() {
    const resultsSection = document.getElementById('results');
    resultsSection.style.display = 'block';
    resultsSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
}

// حساب العلاوة بناءً على مقدار العلاوة وتاريخ الاستحقاق (يوم-شهر-سنة)
function calculateAllowance() {
    const amountInput = document.getElementById('allowanceAmount');
    const dayInput = document.getElementById('allowanceDay');
    const monthInput = document.getElementById('allowanceMonth');
    const yearInput = document.getElementById('allowanceYear');

    const amount = parseFloat(amountInput.value || '0');
    const day = parseInt(dayInput.value || '0', 10);
    const month = parseInt(monthInput.value || '0', 10) - 1; // JavaScript months: 0-11
    const year = parseInt(yearInput.value || '0', 10);

    if (!amount || amount < 0 || !day || !monthInput.value || !year) {
        alert('يرجى إدخال مقدار العلاوة وتاريخ الاستحقاق بشكل صحيح');
        return;
    }

    const startDate = new Date(year, month, day);
    if (isNaN(startDate.getTime())) {
        alert('تاريخ الاستحقاق غير صالح');
        return;
    }

    const now = new Date();

    // حساب عدد الأشهر الكاملة من شهر الاستحقاق حتى الشهر الحالي (بدون النظر للأيام)
    // مثال: من أبريل 2025 إلى أغسطس 2025 = أبريل، مايو، يونيو، يوليو، أغسطس = 5 أشهر
    let months = (now.getFullYear() - startDate.getFullYear()) * 12 + (now.getMonth() - startDate.getMonth()) + 1;

    if (months < 0) months = 0; // لا سالبة

    const total = months * amount;

    // عرض النتيجة
    const resultWrap = document.getElementById('allowanceResult');
    const resultAmount = document.getElementById('allowanceResultAmount');
    resultAmount.textContent = formatNumber(total);
    resultWrap.style.display = 'block';
}

// تبديل التبويبات
function setupTabs() {
    const buttons = document.querySelectorAll('.tab-button');
    const contents = document.querySelectorAll('.tab-content');

    buttons.forEach(btn => {
        btn.addEventListener('click', () => {
            buttons.forEach(b => b.classList.remove('active'));
            contents.forEach(c => c.style.display = 'none');

            btn.classList.add('active');
            const targetId = btn.getAttribute('data-target');
            const targetEl = document.getElementById(targetId);
            if (targetEl) targetEl.style.display = 'block';
        });
    });

    // زر احسب العلاوة
    const calcAllowanceBtn = document.getElementById('calculateAllowanceBtn');
    if (calcAllowanceBtn) {
        calcAllowanceBtn.addEventListener('click', calculateAllowance);
    }
}


// تنسيق الأرقام
function formatNumber(number) {
    return new Intl.NumberFormat('ar-IQ').format(number);
}

// حساب الضريبة من الجدول
function calculateTax(salary, childrenCount) {
    const brackets = taxesData[childrenCount];
    if (!brackets) return 0;

    // البحث في الجدول عن المبلغ المطابق
    for (let bracket of brackets) {
        if (salary >= bracket.min && salary <= bracket.max) {
            return bracket.tax;
        }
    }

    // إذا لم يوجد في الجدول، إرجاع 0
    return 0;
}

// الوظيفة الرئيسية للحساب
function calculate() {
    // التحقق من البيانات
    if (!currentGrade || !currentStep || currentChildren === '') {
        alert('يرجى ملء جميع الحقول المطلوبة');
        return;
    }

    // الحصول على الراتب الأساسي
    const baseSalary = salariesData[currentGrade][currentStep];
    if (!baseSalary) {
        alert('خطأ في البيانات المدخلة');
        return;
    }

    // حساب الضريبة من الجدول
    const taxAmount = calculateTax(baseSalary, currentChildren);

    // عرض النتائج
    displayResults(baseSalary, taxAmount);
    showResults();
}

// عرض النتائج
function displayResults(salary, tax) {
    document.getElementById('salaryAmount').textContent = formatNumber(salary);
    document.getElementById('taxAmount').textContent = formatNumber(tax);

    // إضافة تأثيرات بصرية
    animateNumbers();
}

// تحريك الأرقام
function animateNumbers() {
    const amounts = document.querySelectorAll('.card-amount');
    amounts.forEach(amount => {
        amount.style.transform = 'scale(1.1)';
        setTimeout(() => {
            amount.style.transform = 'scale(1)';
        }, 200);
    });
}
