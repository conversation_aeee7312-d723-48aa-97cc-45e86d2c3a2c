/* إعدادات عامة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'Microsoft Sans Serif', sans-serif;
    background: linear-gradient(135deg, #fff8e1 0%, #ffcc80 100%);
    min-height: 100vh;
    direction: rtl;
    text-align: right;
    overflow-x: hidden;
    font-feature-settings: "lnum" 1;
    unicode-bidi: bidi-override;
}

/* تحسين عرض الأرقام العربية */
* {
    font-variant-numeric: lining-nums;
    -webkit-font-feature-settings: "lnum" 1;
    font-feature-settings: "lnum" 1;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* العنوان المتحرك */
.animated-header {
    text-align: center;
    margin-bottom: 40px;
    padding: 30px 0;
}

.main-title {
    font-size: 4rem;
    font-weight: 900;
    margin-bottom: 20px;
    text-shadow: 3px 3px 6px rgba(0,0,0,0.3);
    background: linear-gradient(45deg, #8e44ad, #3498db);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.word {
    display: inline-block;
    margin: 0 10px;
    animation: wave 3s ease-in-out infinite;
    transition: all 0.3s ease;
}

.word:hover {
    transform: scale(1.1);
    text-shadow: 3px 3px 6px rgba(0,0,0,0.2);
}

.word.blue {
    color: #0077b6;
    animation-delay: 0s;
}

.word.orange {
    color: #ff8500;
    animation-delay: 0.5s;
}

.word:nth-child(3) {
    animation-delay: 1s;
}

.word:nth-child(4) {
    animation-delay: 1.5s;
}

.word:nth-child(5) {
    animation-delay: 2s;
}

@keyframes wave {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

.subtitle {
    font-size: 1.5rem;
    color: #555;
    font-weight: 400;
    opacity: 0;
    animation: fadeInUp 1s ease-out 1s forwards;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* النموذج الرئيسي */
.main-form {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 25px;
    padding: 45px;
    box-shadow: 0 25px 50px rgba(0,0,0,0.15);
    backdrop-filter: blur(15px);
    border: 3px solid transparent;
    background-image: linear-gradient(rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.95)),
                      linear-gradient(45deg, #e74c3c, #3498db, #f39c12, #87ceeb, #9b59b6);
    background-origin: border-box;
    background-clip: content-box, border-box;
    margin-bottom: 30px;
    position: relative;
    animation: borderColorShift 15s ease-in-out infinite;
}

@keyframes borderColorShift {
    0% {
        background-image: linear-gradient(rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.95)),
                          linear-gradient(45deg, #e74c3c, #3498db, #f39c12, #87ceeb, #9b59b6);
    }
    20% {
        background-image: linear-gradient(rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.95)),
                          linear-gradient(45deg, #3498db, #f39c12, #87ceeb, #9b59b6, #e74c3c);
    }
    40% {
        background-image: linear-gradient(rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.95)),
                          linear-gradient(45deg, #f39c12, #87ceeb, #9b59b6, #e74c3c, #3498db);
    }
    60% {
        background-image: linear-gradient(rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.95)),
                          linear-gradient(45deg, #87ceeb, #9b59b6, #e74c3c, #3498db, #f39c12);
    }
    80% {
        background-image: linear-gradient(rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.95)),
                          linear-gradient(45deg, #9b59b6, #e74c3c, #3498db, #f39c12, #87ceeb);
    }
    100% {
        background-image: linear-gradient(rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.95)),
                          linear-gradient(45deg, #e74c3c, #3498db, #f39c12, #87ceeb, #9b59b6);
    }
}

.form-section {
    margin-bottom: 40px;
}

.section-title {
    font-size: 2rem;
    color: #0077b6;
    text-align: center;
    margin-bottom: 30px;
    font-weight: 600;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background: linear-gradient(90deg, #0077b6, #ff8500);
    border-radius: 2px;
}

/* صفوف النموذج */
.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-bottom: 30px;
}

@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
        gap: 20px;
    }
}

.form-group {
    margin-bottom: 25px;
}

.form-label {
    display: block;
    font-size: 1.4rem;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    gap: 12px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.4);
}

.icon {
    font-size: 1.3rem;
}

.form-select, .form-input {
    width: 100%;
    padding: 18px 25px;
    font-size: 1.3rem;
    border: 3px solid #e0e0e0;
    border-radius: 15px;
    background: white;
    color: #333;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    font-family: 'Arial', 'Tahoma', sans-serif;
    font-weight: 600;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.form-select {
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
    background-position: left 15px center;
    background-repeat: no-repeat;
    background-size: 20px;
    padding-left: 50px;
}

.form-select:focus, .form-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.2), 0 8px 25px rgba(0,0,0,0.15);
    transform: translateY(-3px) scale(1.02);
    background: linear-gradient(145deg, #ffffff, #f8f9ff);
}

.form-select:disabled {
    background-color: #f5f5f5;
    color: #999;
    cursor: not-allowed;
}

/* حقول خاصة بتبويب العلاوة */
#allowanceAmount {
    font-size: 1.5rem !important;
    padding: 22px 30px !important;
    font-weight: 700 !important;
    background: linear-gradient(145deg, #fff, #f0f8ff) !important;
    border: 3px solid #4facfe !important;
}

/* زر الحساب */
.calculate-btn {
    width: 100%;
    padding: 22px 35px;
    font-size: 1.5rem;
    font-weight: 700;
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
    border: none;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    margin-top: 35px;
    font-family: 'Arial', 'Tahoma', sans-serif;
    box-shadow: 0 10px 30px rgba(255, 107, 107, 0.4);
    position: relative;
    overflow: hidden;
}



.calculate-btn:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 15px 40px rgba(255, 107, 107, 0.6);
    background: linear-gradient(135deg, #ff5252, #d32f2f);
}

.calculate-btn:active {
    transform: translateY(-2px) scale(0.98);
}

.btn-icon {
    font-size: 1.6rem;
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-5px); }
    60% { transform: translateY(-3px); }
}

/* زر الخروج */
.exit-btn {
    position: fixed;
    bottom: 20px;
    left: 20px;
    padding: 12px 20px;
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 6px 20px rgba(231, 76, 60, 0.4);
    z-index: 1000;
}

.exit-btn:hover {
    transform: translateY(-3px) scale(1.1);
    box-shadow: 0 10px 30px rgba(231, 76, 60, 0.6);
}

/* قسم النتائج */
.results-section {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
    animation: slideInUp 0.6s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.result-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 25px;
    margin-bottom: 30px;
}

.result-card {
    background: linear-gradient(135deg, #ffffff, #f8f9fa);
    border-radius: 15px;
    padding: 30px 20px;
    text-align: center;
    box-shadow: 0 10px 25px rgba(0,0,0,0.08);
    border: 2px solid transparent;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.result-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #0077b6, #ff8500);
}

.result-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.12);
    border-color: rgba(0, 119, 182, 0.3);
}

.salary-card::before {
    background: linear-gradient(90deg, #28a745, #20c997);
}

.tax-card::before {
    background: linear-gradient(90deg, #dc3545, #fd7e14);
}

.net-card::before {
    background: linear-gradient(90deg, #0077b6, #6f42c1);
}

.card-icon {
    font-size: 3rem;
    margin-bottom: 15px;
    opacity: 0.8;
}

.card-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: #555;
    margin-bottom: 15px;
}

.card-amount {
    font-size: 2.5rem;
    font-weight: 700;
    color: #0077b6;
    margin-bottom: 10px;
    direction: ltr;
    text-align: center;
}

.card-currency {
    font-size: 1rem;
    color: #777;
    font-weight: 500;
}

/* تفاصيل الضريبة */
.tax-details {
    background: linear-gradient(135deg, #e3f2fd, #f3e5f5);
    border-radius: 12px;
    padding: 20px;
    text-align: center;
    border: 1px solid rgba(0, 119, 182, 0.2);
}

.tax-rate {
    font-size: 1.3rem;
    font-weight: 600;
    color: #333;
}

.rate-value {
    color: #0077b6;
    font-weight: 700;
    font-size: 1.4rem;
}

/* التذييل */
.footer {
    text-align: center;
    padding: 30px 20px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 15px;
    margin-top: 40px;
    color: #666;
    font-size: 1rem;
    line-height: 1.6;
}

.footer p {
    margin-bottom: 5px;
}

/* تأثيرات الحركة */
.form-group {
    animation: fadeInLeft 0.6s ease-out;
}

.form-group:nth-child(1) { animation-delay: 0.1s; }
.form-group:nth-child(2) { animation-delay: 0.2s; }
.form-group:nth-child(3) { animation-delay: 0.3s; }

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* تنسيقات الهاتف المحمول */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }

    .main-title {
        font-size: 2.5rem;
    }

    .word {
        margin: 0 5px;
        display: inline-block;
    }

    .main-form {
        padding: 25px;
    }

    .result-cards {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .card-amount {
        font-size: 2rem;
    }
}

@media (max-width: 480px) {
    .main-title {
        font-size: 2rem;
    }

    .word {
        display: block;
        margin: 5px 0;
    }

    .form-select {
        padding: 12px 15px;
        font-size: 1rem;
    }

    .calculate-btn {
        padding: 15px 25px;
        font-size: 1.1rem;
    }
}


/* تبويبات */
.tabs {
    display: flex;
    gap: 15px;
    margin-bottom: 25px;
    justify-content: center;
}
.tab-button {
    padding: 15px 25px;
    background: linear-gradient(145deg, #e9f5ff, #f0f8ff);
    border: 3px solid #cfe8ff;
    color: #0077b6;
    border-radius: 20px;
    cursor: pointer;
    font-weight: 700;
    font-size: 1.2rem;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 6px 20px rgba(0,0,0,0.1);
    position: relative;
    overflow: hidden;
}



.tab-button:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.tab-button.active {
    background: linear-gradient(145deg, #667eea, #764ba2);
    color: #fff;
    border-color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.tab-content { display: block; }

/* مدخلات التاريخ */
.date-inputs {
    display: flex;
    align-items: center;
    gap: 12px;
    justify-content: center;
}
.date-input {
    width: 120px !important;
    text-align: center !important;
    font-size: 1.4rem !important;
    padding: 20px 15px !important;
    font-weight: 700 !important;
    background: linear-gradient(145deg, #fff, #f0fff0) !important;
    border: 3px solid #20c997 !important;
    border-radius: 12px !important;
}
.date-sep {
    color: #333;
    font-weight: 900;
    font-size: 1.8rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

/* نتيجة العلاوة */
.allowance-result {
    margin-top: 30px;
    padding: 30px;
    border-radius: 20px;
    background: linear-gradient(145deg, #f1fff4, #e8f5e8);
    border: 3px solid #28a745;
    text-align: center;
    box-shadow: 0 15px 35px rgba(40, 167, 69, 0.3);
    position: relative;
}

.allowance-result::before {
    content: '🎉';
    position: absolute;
    top: 10px;
    right: 15px;
    font-size: 2rem;
}

.allowance-title {
    font-size: 2.5rem;
    font-weight: 900;
    color: #28a745;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
}

.allowance-amount {
    font-size: 2.2rem;
    font-weight: 800;
    color: #1e7e34;
    background: linear-gradient(145deg, #e8fbe9, #d4edda);
    border: 3px solid #28a745;
    padding: 20px 25px;
    border-radius: 15px;
    display: inline-block;
    min-width: 200px;
    box-shadow: 0 8px 20px rgba(40, 167, 69, 0.2);
}

/* تفاصيل الحساب */
.calculation-details {
    margin-top: 15px;
    padding: 15px 20px;
    background: linear-gradient(145deg, #fff3cd, #ffeaa7);
    border: 2px solid #f39c12;
    border-radius: 12px;
    font-size: 1.1rem;
    font-weight: 600;
    color: #d68910;
    text-align: center;
    box-shadow: 0 4px 15px rgba(243, 156, 18, 0.2);
    animation: detailsFadeIn 0.8s ease-out;
}

@keyframes detailsFadeIn {
    0% { opacity: 0; transform: translateY(20px); }
    100% { opacity: 1; transform: translateY(0); }
}

/* تأثيرات إضافية */
.form-select option {
    padding: 10px;
    font-size: 1rem;
}

/* تحسين الألوان للوضع المظلم */
@media (prefers-color-scheme: dark) {
    .main-form, .results-section {
        background: rgba(30, 30, 30, 0.95);
        color: #fff;
    }

    .form-select {
        background: #333;
        color: #fff;
        border-color: #555;
    }

    .card-title {
        color: #ccc;
    }
}
