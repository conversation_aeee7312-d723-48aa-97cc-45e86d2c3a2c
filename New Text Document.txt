سارفع لك ملفين اكسيل الاول باسم (سلم الرواتب2) وبها هناك خيارين بالاسفل نظع رقمين يقوم الاكسيل بتحديد الراتب بلونين افقي وعمودي وتقاطعهم هو حدود الراتب المطلوب والملف الثاني باسم (جدول الضريبة - A) وهذا عند تحديد حدود الراتب فيه من والى ينختار عدد الاولاد يتم اظهار لنا الضريبة المطلوبة اريدك ان تقوم بدمجهم وبرمجتهم بالاكسيل او اكسيز او اي لغة اخرى بحيت عندما اقوم باختيار حدود الراتب بالارقام الدرجة رقم والاستيب رقم ما يظهر لي الراتب الاسمي وتظهر لي خيار عدد الاولاد وعند اختيار ماموجود ف القائمة الخاصة به يظهر مبلغ الضريبة 
اريد النطاق عددد الاولاد كما يلي (a/تدل على الغير متزوج و0/ تدل على المتزوج بدون اطفال و 1 تدل على المتزوج ولديه طفل واحد الى 10 وهكذا)
ولغة التسمية فقط عربية
لا احتساب الضريبة جاهزة لديك في ملف الاصلي للظريبة والية عمل الملف بعد اختيار عدد الاولاد وتحديد حدود الراتب يظهر بالخلية التي اسفل حدود الراتب يظهر مبلغ الضريبة
برمجه بواجه html احترافية تفاعلية 
على ان تقوم بتحليل ورؤية الملف الخاص بالضريبة المقصود الجدول وكل تقاطع عمود وهو عدد الابناء وصف وهو حدود الراتب الاسمي ينتج حد من حدود الضريبة 
اما ملف الراتب الاسمي فبنفس الطريقة العمود يمثل الاستيب والصف يمثل الدرجة وتقاطعهما يمثل حدود الراتب الاسمي 

نعم اريد صفحة HTML ملف JavaScript يحتوي على البيانات ملف CSS لتنسيق الواجهة بواجهه احترافية تفاعلية وبحجم خط كبير وحقول وازرار مودرن تفاعلية وبلون خلفية سمائي + ابيض + ازرق فاتح والازرار لون اخضر واكتب بالاعلى بالمنتصف (المبرمج علي عاجل 
خشان المحنَة) بلون ازرق وبرتقالي وازرق مدموجة تفاعلية متموجة متحركة
